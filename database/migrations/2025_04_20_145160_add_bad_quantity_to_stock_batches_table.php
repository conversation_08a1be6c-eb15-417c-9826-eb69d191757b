<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stock_batches', function (Blueprint $table) {
            $table->integer('bad_quantity')->default(0)->after('quantity')->comment('Stok produk dengan kondisi buruk');
            $table->integer('unusable_quantity')->default(0)->after('bad_quantity')->comment('Stok produk dengan kondisi tidak layak pakai');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stock_batches', function (Blueprint $table) {
            $table->dropColumn(['bad_quantity', 'unusable_quantity']);
        });
    }
};
