<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_out_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_out_id')->constrained('product_outs');
            $table->foreignId('batch_id')->constrained('stock_batches');
            $table->integer('quantity');
            $table->decimal('sale_price', 15, 2);
            $table->decimal('discount', 15, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_out_items');
    }
};
