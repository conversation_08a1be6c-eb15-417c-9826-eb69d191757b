<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_ins', function (Blueprint $table) {
            $table->id();
            $table->date('date_in');
            $table->foreignId('supplier_id')->constrained('suppliers');
            $table->text('note')->nullable();
            $table->foreignId('user_id')->constrained('employees');
            $table->enum('status', [
                'waiting_store_manager_approval',
                'waiting_owner_approval',
                'approved',
                'rejected'
            ])->default('waiting_store_manager_approval');
            $table->foreignId('manager_approved_by')->nullable()->constrained('employees');
            $table->foreignId('owner_approved_by')->nullable()->constrained('employees');
            $table->timestamp('manager_approved_at')->nullable();
            $table->timestamp('owner_approved_at')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_ins');
    }
};
