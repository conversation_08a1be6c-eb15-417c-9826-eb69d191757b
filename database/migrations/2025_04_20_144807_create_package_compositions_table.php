<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('package_compositions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('package_product_id')->constrained('products')->onDelete('cascade')->comment('ID produk paket');
            $table->foreignId('component_product_id')->constrained('products')->onDelete('cascade')->comment('ID produk komponen');
            $table->integer('quantity')->comment('Jumlah komponen dalam paket');
            $table->enum('stock_condition', ['good', 'bad'])->comment('Kondisi stok komponen: good = baik, bad = buruk');
            $table->timestamps();

            // Index unik untuk mencegah duplikasi kombinasi package + component + condition
            $table->unique(['package_product_id', 'component_product_id', 'stock_condition'], 'unique_package_component_condition');
            
            // Index untuk performa query
            $table->index(['package_product_id']);
            $table->index(['component_product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('package_compositions');
    }
};
