<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Change date_in column to datetime in product_ins table
        Schema::table('product_ins', function (Blueprint $table) {
            $table->datetime('date_in')->change();
        });

        // Change date_out column to datetime in product_outs table
        Schema::table('product_outs', function (Blueprint $table) {
            $table->datetime('date_out')->change();
        });

        // Change date_ret column to datetime in product_returns table
        Schema::table('product_returns', function (Blueprint $table) {
            $table->datetime('date_ret')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert date_in column back to date in product_ins table
        Schema::table('product_ins', function (Blueprint $table) {
            $table->date('date_in')->change();
        });

        // Revert date_out column back to date in product_outs table
        Schema::table('product_outs', function (Blueprint $table) {
            $table->date('date_out')->change();
        });

        // Revert date_ret column back to date in product_returns table
        Schema::table('product_returns', function (Blueprint $table) {
            $table->date('date_ret')->change();
        });
    }
};
