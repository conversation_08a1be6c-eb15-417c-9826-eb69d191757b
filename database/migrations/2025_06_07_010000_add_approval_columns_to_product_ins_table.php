<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_ins', function (Blueprint $table) {
            $table->enum('status', [
                'waiting_store_manager_approval',
                'waiting_owner_approval', 
                'approved',
                'rejected'
            ])->default('waiting_store_manager_approval')->after('user_id');
            $table->foreignId('manager_approved_by')->nullable()->constrained('employees')->after('status');
            $table->foreignId('owner_approved_by')->nullable()->constrained('employees')->after('manager_approved_by');
            $table->timestamp('manager_approved_at')->nullable()->after('owner_approved_by');
            $table->timestamp('owner_approved_at')->nullable()->after('manager_approved_at');
            $table->text('rejection_reason')->nullable()->after('owner_approved_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_ins', function (Blueprint $table) {
            $table->dropForeign(['manager_approved_by']);
            $table->dropForeign(['owner_approved_by']);
            $table->dropColumn([
                'status',
                'manager_approved_by',
                'owner_approved_by',
                'manager_approved_at',
                'owner_approved_at',
                'rejection_reason'
            ]);
        });
    }
};
