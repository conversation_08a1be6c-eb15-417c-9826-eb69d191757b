<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_out_items', function (Blueprint $table) {
            // Hapus kolom discount lama (persentase)
            $table->dropColumn('discount');
            
            // Tambah kolom baru untuk sistem diskon
            $table->decimal('final_price', 15, 2)->nullable()->comment('Harga akhir setelah diskon');
            $table->foreignId('discount_id')->nullable()->constrained('discounts')->onDelete('set null')->comment('ID diskon yang digunakan');
            $table->boolean('is_manual_discount')->default(false)->comment('Apakah diskon diinput manual');
            
            // Index untuk performa
            $table->index(['discount_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_out_items', function (Blueprint $table) {
            // Kembalikan kolom discount lama
            $table->decimal('discount', 5, 2)->default(0)->comment('Persentase diskon');
            
            // Hapus kolom baru
            $table->dropForeign(['discount_id']);
            $table->dropColumn(['final_price', 'discount_id', 'is_manual_discount']);
        });
    }
};
