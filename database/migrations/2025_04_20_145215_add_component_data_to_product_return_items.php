<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_return_items', function (Blueprint $table) {
            // Cek apakah kolom belum ada sebelum menambahkan
            if (!Schema::hasColumn('product_return_items', 'component_expiration_dates')) {
                $table->json('component_expiration_dates')->nullable()->after('original_stock_condition')->comment('Data tanggal kadaluarsa komponen untuk produk paket');
            }
            
            if (!Schema::hasColumn('product_return_items', 'component_conditions')) {
                $table->json('component_conditions')->nullable()->after('component_expiration_dates')->comment('Data kondisi komponen untuk produk paket');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_return_items', function (Blueprint $table) {
            if (Schema::hasColumn('product_return_items', 'component_expiration_dates')) {
                $table->dropColumn('component_expiration_dates');
            }
            
            if (Schema::hasColumn('product_return_items', 'component_conditions')) {
                $table->dropColumn('component_conditions');
            }
        });
    }
};
