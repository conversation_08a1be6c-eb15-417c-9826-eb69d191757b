<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_reservations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_out_id')->constrained('product_outs')->onDelete('cascade');
            $table->foreignId('product_out_item_id')->constrained('product_out_items')->onDelete('cascade');
            $table->foreignId('batch_id')->constrained('stock_batches')->onDelete('cascade');
            $table->integer('quantity')->comment('Jumlah yang direservasi');
            $table->enum('stock_condition', ['normal', 'bad'])->default('normal')->comment('Kondisi stock yang direservasi');
            $table->timestamp('reserved_at')->useCurrent()->comment('Waktu reservasi dibuat');
            $table->timestamp('expires_at')->nullable()->comment('Waktu reservasi kadaluarsa (optional)');
            $table->timestamps();

            // Index untuk performance
            $table->index(['product_out_id', 'batch_id']);
            $table->index(['batch_id', 'stock_condition']);
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_reservations');
    }
};
