<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_outs', function (Blueprint $table) {
            $table->enum('stock_type', ['normal', 'bad', 'mixed'])->default('normal')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_outs', function (Blueprint $table) {
            $table->enum('stock_type', ['normal', 'bad'])->default('normal')->change();
        });
    }
};
