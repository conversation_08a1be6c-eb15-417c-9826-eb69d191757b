<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_opname_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('opname_id')->constrained('stock_opnames');
            $table->foreignId('batch_id')->constrained('stock_batches');
            $table->integer('system_qty_good')->default(0)->comment('Stok sistem kondisi baik');
            $table->integer('actual_qty_good')->default(0)->comment('Stok aktual kondisi baik');
            $table->integer('system_qty_bad')->default(0)->comment('Stok sistem kondisi buruk');
            $table->integer('actual_qty_bad')->default(0)->comment('Stok aktual kondisi buruk');
            $table->integer('system_qty_unusable')->default(0)->comment('Stok sistem kondisi tidak layak pakai');
            $table->integer('actual_qty_unusable')->default(0)->comment('Stok aktual kondisi tidak layak pakai');
            // Keep legacy fields for backward compatibility
            $table->integer('system_qty')->default(0)->comment('Legacy field - stok sistem total');
            $table->integer('actual_qty')->default(0)->comment('Legacy field - stok aktual total');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_opname_items');
    }
};
