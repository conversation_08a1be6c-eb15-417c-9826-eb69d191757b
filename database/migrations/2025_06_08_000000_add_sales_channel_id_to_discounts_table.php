<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('discounts', function (Blueprint $table) {
            $table->foreignId('sales_channel_id')->nullable()->after('name')->constrained('sales_channels')->onDelete('cascade')->comment('Saluran penjualan (nullable untuk semua saluran)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('discounts', function (Blueprint $table) {
            $table->dropForeign(['sales_channel_id']);
            $table->dropColumn('sales_channel_id');
        });
    }
};
