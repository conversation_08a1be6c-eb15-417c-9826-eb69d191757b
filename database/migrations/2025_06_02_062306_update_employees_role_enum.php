<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update existing data to match enum values
        DB::table('employees')->update(['role' => 'owner']);

        Schema::table('employees', function (Blueprint $table) {
            // Update role column to use enum values
            $table->enum('role', ['owner', 'store_manager', 'packing_admin'])->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Revert back to string
            $table->string('role')->change();
        });
    }
};
