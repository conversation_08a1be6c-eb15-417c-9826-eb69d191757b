<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_return_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_return_id')->constrained('product_returns');
            $table->foreignId('product_id')->constrained('products');
            $table->foreignId('batch_id')->nullable()->constrained('stock_batches');
            $table->date('expiration_date');
            $table->integer('quantity');
            $table->string('condition', 10);
            $table->foreignId('package_product_id')->nullable()->constrained('products')->comment('ID produk paket original jika item ini berasal dari breakdown paket');
            $table->string('package_group_id')->nullable()->comment('UUID untuk mengelompokkan komponen dari paket yang sama');
            $table->string('original_stock_condition', 10)->nullable()->comment('Kondisi stok original komponen dalam paket (good/bad)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_return_items');
    }
};
