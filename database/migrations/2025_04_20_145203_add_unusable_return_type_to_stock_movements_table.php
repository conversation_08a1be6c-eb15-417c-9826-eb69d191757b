<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stock_movements', function (Blueprint $table) {
            // Update enum to include UNUSABLE_RETURN type
            $table->enum('type', ['IN', 'OUT', 'RETURN', 'BAD_RETURN', 'UNUSABLE_RETURN', 'OPNAME'])->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stock_movements', function (Blueprint $table) {
            // Revert back to previous enum values
            $table->enum('type', ['IN', 'OUT', 'RETURN', 'BAD_RETURN', 'OPNAME'])->change();
        });
    }
};
