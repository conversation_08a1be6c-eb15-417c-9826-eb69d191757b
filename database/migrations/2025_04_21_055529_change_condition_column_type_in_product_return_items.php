<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_return_items', function (Blueprint $table) {
            // Hapus kolom enum yang lama
            $table->dropColumn('condition');
        });

        Schema::table('product_return_items', function (Blueprint $table) {
            // Buat kolom baru dengan tipe string
            $table->string('condition', 10)->after('quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_return_items', function (Blueprint $table) {
            // Hapus kolom string
            $table->dropColumn('condition');
        });

        Schema::table('product_return_items', function (Blueprint $table) {
            // Kembalikan ke tipe enum
            $table->enum('condition', ['good', 'bad'])->after('quantity');
        });
    }
};
