<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_return_items', function (Blueprint $table) {
            // Cek apakah kolom belum ada sebelum menambahkan
            if (!Schema::hasColumn('product_return_items', 'package_product_id')) {
                $table->foreignId('package_product_id')->nullable()->after('condition')->constrained('products')->comment('ID produk paket original jika item ini berasal dari breakdown paket');
            }
            
            if (!Schema::hasColumn('product_return_items', 'package_group_id')) {
                $table->string('package_group_id')->nullable()->after('package_product_id')->comment('UUID untuk mengelompokkan komponen dari paket yang sama');
            }
            
            if (!Schema::hasColumn('product_return_items', 'original_stock_condition')) {
                $table->string('original_stock_condition', 10)->nullable()->after('package_group_id')->comment('Kondisi stok original komponen dalam paket (good/bad)');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_return_items', function (Blueprint $table) {
            if (Schema::hasColumn('product_return_items', 'package_product_id')) {
                $table->dropForeign(['package_product_id']);
                $table->dropColumn('package_product_id');
            }
            
            if (Schema::hasColumn('product_return_items', 'package_group_id')) {
                $table->dropColumn('package_group_id');
            }
            
            if (Schema::hasColumn('product_return_items', 'original_stock_condition')) {
                $table->dropColumn('original_stock_condition');
            }
        });
    }
};
