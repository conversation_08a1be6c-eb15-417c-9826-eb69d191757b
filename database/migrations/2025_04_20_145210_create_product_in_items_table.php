<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_in_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_in_id')->constrained('product_ins');
            $table->foreignId('batch_id')->nullable()->constrained('stock_batches');
            $table->foreignId('product_id')->constrained('products');
            $table->date('expiration_date');
            $table->integer('quantity');
            $table->decimal('purchase_price', 15, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_in_items');
    }
};
