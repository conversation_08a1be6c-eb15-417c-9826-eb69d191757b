<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_in_items', function (Blueprint $table) {
            $table->enum('stock_condition', ['good', 'bad'])->nullable()->after('package_group_id')->comment('Kondisi stok spesifik untuk item ini (good/bad)');
        });

        Schema::table('product_out_items', function (Blueprint $table) {
            $table->enum('stock_condition', ['good', 'bad'])->nullable()->after('package_group_id')->comment('Kondisi stok spesifik untuk item ini (good/bad)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_in_items', function (Blueprint $table) {
            $table->dropColumn('stock_condition');
        });

        Schema::table('product_out_items', function (Blueprint $table) {
            $table->dropColumn('stock_condition');
        });
    }
};
