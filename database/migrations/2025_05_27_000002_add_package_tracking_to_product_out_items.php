<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_out_items', function (Blueprint $table) {
            // Kolom untuk melacak paket original
            $table->foreignId('package_product_id')->nullable()->after('batch_id')->constrained('products')->comment('ID produk paket original jika item ini berasal dari breakdown paket');
            $table->integer('package_quantity')->nullable()->after('package_product_id')->comment('Jumlah paket original yang di-breakdown');
            $table->string('package_group_id')->nullable()->after('package_quantity')->comment('UUID untuk mengelompokkan komponen dari paket yang sama');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_out_items', function (Blueprint $table) {
            $table->dropForeign(['package_product_id']);
            $table->dropColumn(['package_product_id', 'package_quantity', 'package_group_id']);
        });
    }
};
