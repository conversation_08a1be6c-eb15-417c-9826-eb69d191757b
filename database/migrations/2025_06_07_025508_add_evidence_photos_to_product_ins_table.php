<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_ins', function (Blueprint $table) {
            $table->json('evidence_photos')->nullable()->comment('Foto bukti produk masuk');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_ins', function (Blueprint $table) {
            $table->dropColumn('evidence_photos');
        });
    }
};
