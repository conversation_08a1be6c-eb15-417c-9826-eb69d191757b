<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_return_items', function (Blueprint $table) {
            if (!Schema::hasColumn('product_return_items', 'batch_id')) {
                $table->foreignId('batch_id')->nullable()->after('product_id')->constrained('stock_batches');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_return_items', function (Blueprint $table) {
            if (Schema::hasColumn('product_return_items', 'batch_id')) {
                $table->dropForeign(['batch_id']);
                $table->dropColumn('batch_id');
            }
        });
    }
};
