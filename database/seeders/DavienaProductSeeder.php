<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DavienaProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat brand Daviena jika belum ada
        $davienaId = DB::table('brands')->where('name', 'Daviena')->first()?->id;

        if (!$davienaId) {
            $davienaId = DB::table('brands')->insertGetId([
                'name' => 'Daviena',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $this->command->info('Brand Daviena berhasil dibuat.');
        } else {
            $this->command->info('Brand Daviena sudah ada.');
        }

        // PRODUK SATUAN (ECER)
        $singleProducts = [
            // Facial Wash
            ['name' => 'Facial Wash Gold', 'purchase_price' => 40000, 'sale_price' => 65000],
            ['name' => 'Facial Wash Glowing', 'purchase_price' => 40000, 'sale_price' => 65000],
            ['name' => 'Facial Wash Acne', 'purchase_price' => 40000, 'sale_price' => 65000],

            // Toner
            ['name' => 'Toner Gold', 'purchase_price' => 40000, 'sale_price' => 65000],
            ['name' => 'Toner Glowing', 'purchase_price' => 40000, 'sale_price' => 65000],
            ['name' => 'Toner Acne', 'purchase_price' => 40000, 'sale_price' => 65000],

            // Serum
            ['name' => 'Serum Gold', 'purchase_price' => 45000, 'sale_price' => 70000],
            ['name' => 'Serum Vit C', 'purchase_price' => 45000, 'sale_price' => 70000],
            ['name' => 'Acne Gel Strawberry', 'purchase_price' => 45000, 'sale_price' => 70000],

            // Day Cream
            ['name' => 'Day Cream Gold', 'purchase_price' => 40000, 'sale_price' => 65000],
            ['name' => 'Day Cream Glowing', 'purchase_price' => 40000, 'sale_price' => 65000],
            ['name' => 'Day Cream Acne', 'purchase_price' => 40000, 'sale_price' => 65000],

            // Night Cream
            ['name' => 'Night Cream Gold', 'purchase_price' => 45000, 'sale_price' => 70000],
            ['name' => 'Night Cream Glowing', 'purchase_price' => 45000, 'sale_price' => 70000],
            ['name' => 'Night Cream Acne', 'purchase_price' => 45000, 'sale_price' => 70000],
        ];

        // Insert produk satuan
        foreach ($singleProducts as $product) {
            DB::table('products')->insertOrIgnore([
                'name' => $product['name'],
                'brand_id' => $davienaId,
                'sku' => '', // SKU kosong sesuai permintaan
                'type' => 'single',
                'default_purchase_price' => $product['purchase_price'],
                'default_sale_price' => $product['sale_price'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // PRODUK NON MITRA (tanpa harga)
        $nonMitraProducts = [
            'Retinol', 'Retinal', 'Eye Serum', 'Lip Serum', 'Fresh Mouth',
            'Cooper Peptide', 'Body Lotion 27X Plus', 'White Up Smooth', 'Pore Refining',
            'Cushion Natural', 'Cushion Ivory', 'Refill Natural', 'Refill Ivory',
            'Sunscreen SPF 50', 'Sunscreen SPF 35', 'Serum HA', 'Moist Pink',
            'Moist Duo', 'Exfoliating Pad', 'Acne Care', 'Sabun Vampire'
        ];

        // Insert produk non mitra
        foreach ($nonMitraProducts as $productName) {
            DB::table('products')->insertOrIgnore([
                'name' => $productName,
                'brand_id' => $davienaId,
                'sku' => '', // SKU kosong sesuai permintaan
                'type' => 'single',
                'default_purchase_price' => 0, // Harga kosong
                'default_sale_price' => 0, // Harga kosong
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // PRODUK PAKET LENGKAP
        $packageProducts = [
            ['name' => 'PAKET LENGKAP GOLD', 'purchase_price' => 200000, 'sale_price' => 300000],
            ['name' => 'PAKET LENGKAP GLOWING', 'purchase_price' => 200000, 'sale_price' => 300000],
            ['name' => 'PAKET LENGKAP ACNE', 'purchase_price' => 200000, 'sale_price' => 300000],
        ];

        // Insert produk paket lengkap
        foreach ($packageProducts as $product) {
            DB::table('products')->insertOrIgnore([
                'name' => $product['name'],
                'brand_id' => $davienaId,
                'sku' => '', // SKU kosong sesuai permintaan
                'type' => 'package',
                'default_purchase_price' => $product['purchase_price'],
                'default_sale_price' => $product['sale_price'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // PRODUK PAKET HEMAT (tanpa harga)
        $hematPackages = [
            'PAKET HEMAT GOLD',
            'PAKET HEMAT GLOWING',
            'PAKET HEMAT ACNE 1',
            'PAKET HEMAT ACNE 2'
        ];

        // Insert produk paket hemat
        foreach ($hematPackages as $productName) {
            DB::table('products')->insertOrIgnore([
                'name' => $productName,
                'brand_id' => $davienaId,
                'sku' => '', // SKU kosong sesuai permintaan
                'type' => 'package',
                'default_purchase_price' => 0, // Harga kosong
                'default_sale_price' => 0, // Harga kosong
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // KOMPOSISI PAKET LENGKAP
        $this->createPackageCompositions($davienaId);

        // KOMPOSISI PAKET HEMAT
        $this->createHematPackageCompositions($davienaId);

        $this->command->info('Produk Daviena berhasil ditambahkan!');
    }

    /**
     * Membuat komposisi untuk paket lengkap
     */
    private function createPackageCompositions($davienaId)
    {
        // Ambil ID produk paket lengkap
        $paketLengkapGold = DB::table('products')->where('name', 'PAKET LENGKAP GOLD')->where('brand_id', $davienaId)->first()?->id;
        $paketLengkapGlowing = DB::table('products')->where('name', 'PAKET LENGKAP GLOWING')->where('brand_id', $davienaId)->first()?->id;
        $paketLengkapAcne = DB::table('products')->where('name', 'PAKET LENGKAP ACNE')->where('brand_id', $davienaId)->first()?->id;

        // Ambil ID produk komponen
        $facialWashGold = DB::table('products')->where('name', 'Facial Wash Gold')->where('brand_id', $davienaId)->first()?->id;
        $tonerGold = DB::table('products')->where('name', 'Toner Gold')->where('brand_id', $davienaId)->first()?->id;
        $serumGold = DB::table('products')->where('name', 'Serum Gold')->where('brand_id', $davienaId)->first()?->id;
        $dayCreamGold = DB::table('products')->where('name', 'Day Cream Gold')->where('brand_id', $davienaId)->first()?->id;
        $nightCreamGold = DB::table('products')->where('name', 'Night Cream Gold')->where('brand_id', $davienaId)->first()?->id;

        $facialWashGlowing = DB::table('products')->where('name', 'Facial Wash Glowing')->where('brand_id', $davienaId)->first()?->id;
        $tonerGlowing = DB::table('products')->where('name', 'Toner Glowing')->where('brand_id', $davienaId)->first()?->id;
        $serumVitC = DB::table('products')->where('name', 'Serum Vit C')->where('brand_id', $davienaId)->first()?->id;
        $dayCreamGlowing = DB::table('products')->where('name', 'Day Cream Glowing')->where('brand_id', $davienaId)->first()?->id;
        $nightCreamGlowing = DB::table('products')->where('name', 'Night Cream Glowing')->where('brand_id', $davienaId)->first()?->id;

        $facialWashAcne = DB::table('products')->where('name', 'Facial Wash Acne')->where('brand_id', $davienaId)->first()?->id;
        $tonerAcne = DB::table('products')->where('name', 'Toner Acne')->where('brand_id', $davienaId)->first()?->id;
        $acneGelStrawberry = DB::table('products')->where('name', 'Acne Gel Strawberry')->where('brand_id', $davienaId)->first()?->id;
        $dayCreamAcne = DB::table('products')->where('name', 'Day Cream Acne')->where('brand_id', $davienaId)->first()?->id;
        $nightCreamAcne = DB::table('products')->where('name', 'Night Cream Acne')->where('brand_id', $davienaId)->first()?->id;

        // Komposisi PAKET LENGKAP GOLD
        if ($paketLengkapGold) {
            $goldComponents = [
                ['component_id' => $facialWashGold, 'quantity' => 1],
                ['component_id' => $tonerGold, 'quantity' => 1],
                ['component_id' => $serumGold, 'quantity' => 1],
                ['component_id' => $dayCreamGold, 'quantity' => 1],
                ['component_id' => $nightCreamGold, 'quantity' => 1],
            ];

            foreach ($goldComponents as $component) {
                if ($component['component_id']) {
                    // Kondisi good saja
                    DB::table('package_compositions')->insertOrIgnore([
                        'package_product_id' => $paketLengkapGold,
                        'component_product_id' => $component['component_id'],
                        'quantity' => $component['quantity'],
                        'stock_condition' => 'good',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Komposisi PAKET LENGKAP GLOWING
        if ($paketLengkapGlowing) {
            $glowingComponents = [
                ['component_id' => $facialWashGlowing, 'quantity' => 1],
                ['component_id' => $tonerGlowing, 'quantity' => 1],
                ['component_id' => $serumVitC, 'quantity' => 1],
                ['component_id' => $dayCreamGlowing, 'quantity' => 1],
                ['component_id' => $nightCreamGlowing, 'quantity' => 1],
            ];

            foreach ($glowingComponents as $component) {
                if ($component['component_id']) {
                    // Kondisi good saja
                    DB::table('package_compositions')->insertOrIgnore([
                        'package_product_id' => $paketLengkapGlowing,
                        'component_product_id' => $component['component_id'],
                        'quantity' => $component['quantity'],
                        'stock_condition' => 'good',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Komposisi PAKET LENGKAP ACNE
        if ($paketLengkapAcne) {
            $acneComponents = [
                ['component_id' => $facialWashAcne, 'quantity' => 1],
                ['component_id' => $tonerAcne, 'quantity' => 1],
                ['component_id' => $acneGelStrawberry, 'quantity' => 1],
                ['component_id' => $dayCreamAcne, 'quantity' => 1],
                ['component_id' => $nightCreamAcne, 'quantity' => 1],
            ];

            foreach ($acneComponents as $component) {
                if ($component['component_id']) {
                    // Kondisi good saja
                    DB::table('package_compositions')->insertOrIgnore([
                        'package_product_id' => $paketLengkapAcne,
                        'component_product_id' => $component['component_id'],
                        'quantity' => $component['quantity'],
                        'stock_condition' => 'good',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Membuat komposisi untuk paket hemat
     */
    private function createHematPackageCompositions($davienaId)
    {
        // Ambil ID produk paket hemat
        $paketHematGold = DB::table('products')->where('name', 'PAKET HEMAT GOLD')->where('brand_id', $davienaId)->first()?->id;
        $paketHematGlowing = DB::table('products')->where('name', 'PAKET HEMAT GLOWING')->where('brand_id', $davienaId)->first()?->id;
        $paketHematAcne1 = DB::table('products')->where('name', 'PAKET HEMAT ACNE 1')->where('brand_id', $davienaId)->first()?->id;
        $paketHematAcne2 = DB::table('products')->where('name', 'PAKET HEMAT ACNE 2')->where('brand_id', $davienaId)->first()?->id;

        // Ambil ID produk komponen
        $facialWashGold = DB::table('products')->where('name', 'Facial Wash Gold')->where('brand_id', $davienaId)->first()?->id;
        $dayCreamGold = DB::table('products')->where('name', 'Day Cream Gold')->where('brand_id', $davienaId)->first()?->id;
        $nightCreamGold = DB::table('products')->where('name', 'Night Cream Gold')->where('brand_id', $davienaId)->first()?->id;

        $facialWashGlowing = DB::table('products')->where('name', 'Facial Wash Glowing')->where('brand_id', $davienaId)->first()?->id;
        $dayCreamGlowing = DB::table('products')->where('name', 'Day Cream Glowing')->where('brand_id', $davienaId)->first()?->id;
        $nightCreamGlowing = DB::table('products')->where('name', 'Night Cream Glowing')->where('brand_id', $davienaId)->first()?->id;

        $facialWashAcne = DB::table('products')->where('name', 'Facial Wash Acne')->where('brand_id', $davienaId)->first()?->id;
        $dayCreamAcne = DB::table('products')->where('name', 'Day Cream Acne')->where('brand_id', $davienaId)->first()?->id;
        $nightCreamAcne = DB::table('products')->where('name', 'Night Cream Acne')->where('brand_id', $davienaId)->first()?->id;
        $acneGelStrawberry = DB::table('products')->where('name', 'Acne Gel Strawberry')->where('brand_id', $davienaId)->first()?->id;

        // Komposisi PAKET HEMAT GOLD (Facial Wash, Day Cream, Night Cream)
        if ($paketHematGold) {
            $hematGoldComponents = [
                ['component_id' => $facialWashGold, 'quantity' => 1],
                ['component_id' => $dayCreamGold, 'quantity' => 1],
                ['component_id' => $nightCreamGold, 'quantity' => 1],
            ];

            foreach ($hematGoldComponents as $component) {
                if ($component['component_id']) {
                    // Kondisi good saja
                    DB::table('package_compositions')->insertOrIgnore([
                        'package_product_id' => $paketHematGold,
                        'component_product_id' => $component['component_id'],
                        'quantity' => $component['quantity'],
                        'stock_condition' => 'good',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Komposisi PAKET HEMAT GLOWING (Facial Wash, Day Cream, Night Cream)
        if ($paketHematGlowing) {
            $hematGlowingComponents = [
                ['component_id' => $facialWashGlowing, 'quantity' => 1],
                ['component_id' => $dayCreamGlowing, 'quantity' => 1],
                ['component_id' => $nightCreamGlowing, 'quantity' => 1],
            ];

            foreach ($hematGlowingComponents as $component) {
                if ($component['component_id']) {
                    // Kondisi good saja
                    DB::table('package_compositions')->insertOrIgnore([
                        'package_product_id' => $paketHematGlowing,
                        'component_product_id' => $component['component_id'],
                        'quantity' => $component['quantity'],
                        'stock_condition' => 'good',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Komposisi PAKET HEMAT ACNE 1 (Facial Wash, Day Cream, Night Cream)
        if ($paketHematAcne1) {
            $hematAcne1Components = [
                ['component_id' => $facialWashAcne, 'quantity' => 1],
                ['component_id' => $dayCreamAcne, 'quantity' => 1],
                ['component_id' => $nightCreamAcne, 'quantity' => 1],
            ];

            foreach ($hematAcne1Components as $component) {
                if ($component['component_id']) {
                    // Kondisi good saja
                    DB::table('package_compositions')->insertOrIgnore([
                        'package_product_id' => $paketHematAcne1,
                        'component_product_id' => $component['component_id'],
                        'quantity' => $component['quantity'],
                        'stock_condition' => 'good',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Komposisi PAKET HEMAT ACNE 2 (Facial Wash, Day Cream, Acne Gel Strawberry)
        if ($paketHematAcne2) {
            $hematAcne2Components = [
                ['component_id' => $facialWashAcne, 'quantity' => 1],
                ['component_id' => $dayCreamAcne, 'quantity' => 1],
                ['component_id' => $acneGelStrawberry, 'quantity' => 1],
            ];

            foreach ($hematAcne2Components as $component) {
                if ($component['component_id']) {
                    // Kondisi good saja
                    DB::table('package_compositions')->insertOrIgnore([
                        'package_product_id' => $paketHematAcne2,
                        'component_product_id' => $component['component_id'],
                        'quantity' => $component['quantity'],
                        'stock_condition' => 'good',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }
}
