<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\Employee;
use App\Models\EmployeeRole;

class MasterDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        DB::table('suppliers')->insertOrIgnore([
            ['name' => 'PT Skincare Nusantara'],
            ['name' => 'PT Beauty Supply'],
        ]);
        DB::table('sales_channels')->insertOrIgnore([
            ['name' => 'TikTok'],
            ['name' => 'Shopee'],
            ['name' => 'Offline Store'],
        ]);
        // Create employees with proper roles
        Employee::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Owner',
                'role' => EmployeeRole::OWNER,
                'password' => Hash::make('password'),
            ]
        );

        Employee::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Kepala Toko',
                'role' => EmployeeRole::STORE_MANAGER,
                'password' => Hash::make('password'),
            ]
        );

        Employee::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin Packing',
                'role' => EmployeeRole::PACKING_ADMIN,
                'password' => Hash::make('password'),
            ]
        );


    }
}
