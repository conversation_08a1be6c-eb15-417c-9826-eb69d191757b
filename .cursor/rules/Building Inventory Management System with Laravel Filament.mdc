---
description:
globs:
alwaysApply: false
---
# Building Inventory Management System with Laravel Filament v3

## 1. Project Setup

1. Create a new Laravel project
2. Install Filament v3 and its required dependencies
3. Configure the database connection in .env file
4. Set up authentication for employees/users

## 2. Create Database Migrations

1. Create migrations for master data tables:
    - brands
    - products
    - employees
    - suppliers
    - sales_channels
2. Create migrations for inventory management tables:
    - stock_batches
    - stock_movements
    - product_ins
    - product_in_items
    - product_outs
    - product_out_items
    - product_returns
    - product_return_items
    - stock_opnames
    - stock_opname_items
3. Add foreign key constraints to establish relationships between tables

## 3. Create Models with Relationships

1. Create Eloquent models for all tables with proper relationships:
    - Brand (hasMany products)
    - Product (belongsTo brand, hasMany stock_batches)
    - Employee (hasMany transactions as user)
    - Supplier (hasMany product_ins)
    - SalesChannel (hasMany product_outs, hasMany product_returns)
    - StockBatch (belongsTo product, hasMany stock_movements)
    - StockMovement (belongsTo stock_batch, belongsTo employee as user)
    - ProductIn (belongsTo supplier, belongsTo employee as user, hasMany product_in_items)
    - ProductInItem (belongsTo product_in, belongsTo product, belongsTo stock_batch)
    - ProductOut (belongsTo sales_channel, belongsTo employee as user, hasMany product_out_items)
    - ProductOutItem (belongsTo product_out, belongsTo stock_batch)
    - ProductReturn (belongsTo sales_channel, belongsTo employee as user, hasMany product_return_items)
    - ProductReturnItem (belongsTo product_return, belongsTo product)
    - StockOpname (belongsTo employee as user, hasMany stock_opname_items)
    - StockOpnameItem (belongsTo stock_opname, belongsTo stock_batch)

## 4. Create Filament Resources

1. Set up Filament Admin Panel
2. Create resources for master data:
    - BrandResource
    - ProductResource
    - EmployeeResource
    - SupplierResource
    - SalesChannelResource
3. Create resources for transaction data:
    - ProductInResource
    - ProductOutResource
    - ProductReturnResource
    - StockOpnameResource
    - StockResource (for checking current stock)

## 5. Master Data Resources Implementation

For each resource, implement:

1. Table view with required columns
2. Form with required fields
3. Relationships (e.g., products belong to brands)

## 6. Transaction Resources Implementation

### ProductInResource:

1. Create list view with columns: date_in, supplier, product count, total quantity
2. Create form with:
    - Date input for date_in
    - Select for supplier_id
    - Text input for notes
    - Repeater for items with:
        - Product select
        - Quantity input
        - Expiration date picker
        - Purchase price input
3. Implement logic to:
    - Create or update stock_batch records
    - Create stock_movement records with "IN" type
    - Record the user who created the transaction

### ProductOutResource:

1. Create list view with columns: date_out, channel, product count, total quantity
2. Create form with:
    - Date input for date_out
    - Select for channel_id
    - Text input for notes
    - Repeater for items with:
        - Product select
        - Quantity input
        - Sale price input
        - Discount input (optional)
3. Implement FEFO (First Expired First Out) and cost optimization logic:
    - Select batches with earliest expiration date and lowest cost
    - Create stock_movement records with "OUT" type
    - Show selected expiration dates in detail view

### ProductReturnResource:

1. Create list view with columns: date_ret, channel, product count, total quantity
2. Create form with:
    - Date input for date_ret
    - Select for channel_id
    - Text input for notes
    - Repeater for items with:
        - Product select
        - Expiration date picker
        - Quantity input
        - Condition select (good/bad/unusable)
3. Implement logic for handling returns:
    - For good condition items: update stock_batch if expiration date exists, set to the lowest purchase price if same expiration date exist.
    - Create stock_movement records with "RETURN" type
    - Handle rejected items for bad and unusable condition returns (not returned to stock)

### StockOpnameResource:

1. Create list view with columns: date_opname, notes
2. Create form with:
    - Date input for date_opname
    - Text input for notes
    - Repeater for items with:
        - Product select
        - Expiration date select
        - Purchase price select
        - System quantity (displayed)
        - Actual quantity input
3. Implement logic to:
    - Update stock quantities
    - Create stock_movement records with "OPNAME" type

### StockResource:

1. Create list view with columns: product, brand, total stock
2. Create detail view with:
    - Product information
    - Table of batches with expiration dates, purchase prices, quantities
    - Button to view movement history for each batch

## 7. Implement Business Logic

1. Create services/actions for complex business logic:
    - StockBatchService: Handle batch creation, updates, and FEFO logic
    - StockMovementService: Record all stock movements
    - ProductOutService: Implement FEFO and cost optimization algorithms
2. Set up observers for models to trigger proper events

## 8. Create Stock Report

1. Create a StockReportResource with filters for:
    - Date range
    - Product
    - Transaction type
    - etc.
2. Implement the report view with all required columns:
    - Transaction date
    - Product name
    - Expiration date
    - Transaction type
    - Quantity change
    - Stock level
    - Purchase price
    - Sale price (if applicable)
    - Stock value
    - Notes

## 9. UI/UX Improvements

1. Customize the dashboard with key metrics:
    - Products with low stock
    - Products nearing expiration
    - Recent transactions
2. Add Filament widgets for quick actions and data visualization:
    - Stock value over time
    - Transaction summary
    - Top products sold

## 10. Access Control and Permissions

1. Implement Filament Shield or custom policies for access control
2. Set up roles and permissions based on employee roles
3. Restrict access to specific resources/actions based on permissions

## 11. Testing

1. Write feature tests for critical functionality:
    - Stock movements
    - FEFO implementation
    - Return processing
2. Test UI flows for all major transactions

## 12. Final Touches

1. Add input validation and error handling
2. Implement notifications for critical events (low stock, expiring products)
3. Add custom filters for more efficient data searching
4. Optimize database queries for performance
5. Add documentation for end users

This step-by-step approach provides a comprehensive roadmap for an AI agent to build the inventory management system according to the provided PRD, utilizing Laravel Filament v3's powerful features.