---
description:
globs:
alwaysApply: false
---
# DB Schema

# Master Data

### **brand**

- `id` (PK, int, auto-increment)
- `nama` (varchar)

### **product**

- `id` (PK)
- `nama` (varchar)
- `brand_id` (FK → brand.id)
- `sku` (varchar)
- `harga_beli_default` (decimal)
- `harga_jual_default` (decimal)

### **employee**

- `id` (PK)
- `nama` (varchar)
- `role` (varchar)

### **supplier**

- `id` (PK)
- `nama` (varchar)

### **sales_channel**

- `id` (PK)
- `nama` (varchar)

# Stok & Batch

### **stock_batch**

(menyimpan stok per tanggal kedaluwarsa + harga beli)

- `id` (PK)
- `product_id` (FK → product.id)
- `expiration_date` (date)
- `purchase_price` (decimal)
- `quantity` (int)

### **stock_movement**

(riwayat IN/OUT/RETURN/OPNAME)

- `id` (PK)
- `batch_id` (FK → stock_batch.id)
- `movement_date` (datetime)
- `type` (enum: IN, OUT, RETURN, OPNAME)
- `quantity` (int)
- `description` (text)
- `user_id` (FK → employee.id)

# Transaksi

## 3.1. Produk Masuk

### **product_in**

- `id` (PK)
- `date_in` (date)
- `supplier_id` (FK → supplier.id)
- `note` (text, nullable)
- `user_id` (FK → employee.id)

### **product_in_item**

- `id` (PK)
- `product_in_id` (FK → product_in.id)
- `batch_id` (FK → stock_batch.id, nullable – dibuat saat INSERT)
- `product_id` (FK → product.id)
- `expiration_date` (date)
- `quantity` (int)
- `purchase_price` (decimal)

## 3.2. Produk Keluar

### **product_out**

- `id` (PK)
- `date_out` (date)
- `channel_id` (FK → sales_channel.id)
- `note` (text, nullable)
- `user_id` (FK → employee.id)

### **product_out_item**

- `id` (PK)
- `product_out_id` (FK → product_out.id)
- `batch_id` (FK → stock_batch.id)
- `quantity` (int)
- `sale_price` (decimal)
- `discount` (decimal, nullable)

## 3.3. Produk Retur

### **product_return**

- `id` (PK)
- `date_ret` (date)
- `channel_id` (FK → sales_channel.id)
- `note` (text, nullable)
- `user_id` (FK → employee.id)

### **product_return_item**

- `id` (PK)
- `product_return_id` (FK → product_return.id)
- `product_id` (FK → product.id)
- `expiration_date` (date)
- `quantity` (int)
- `condition` (string: good, bad, unusable)

## 3.4. Stok Opname

### **stock_opname**

- `id` (PK)
- `date_opname` (date)
- `note` (text, nullable)
- `user_id` (FK → employee.id)

### **stock_opname_item**

- `id` (PK)
- `opname_id` (FK → stock_opname.id)
- `batch_id` (FK → stock_batch.id)
- `system_qty` (int)
- `actual_qty` (int)

---

# Relasi

## 🔗 **1. Master Data**

- **product.brand_id → brand.id**
  Setiap produk hanya punya satu brand (many-to-one).
- **product_in.supplier_id → supplier.id**
  Produk masuk selalu berasal dari satu supplier (many-to-one).
- **product_out.channel_id & product_return.channel_id → sales_channel.id**
  Produk keluar dan retur terhubung ke satu channel penjualan (many-to-one).
- **Semua transaksi (in, out, return, opname) punya user**
  `user_id` di tiap transaksi → employee.id (many-to-one)

## 🧃 **2. Stock & Batch**

- **stock_batch.product_id → product.id**
  Satu batch cuma untuk satu produk (many-to-one).
- **stock_movement.batch_id → stock_batch.id**
  Setiap pergerakan stok mengacu ke satu batch tertentu (many-to-one).
- **stock_movement.user_id → employee.id**
  Mencatat siapa yang melakukan aksi stok.

## 🔁 **3. Transaksi**

### 3.1 Produk Masuk

- **product_in_item.product_in_id → product_in.id**
  Detail masuk (bisa banyak) terhubung ke satu transaksi produk masuk (one-to-many).
- **product_in_item.product_id → product.id**
  Setiap detail masuk menunjukkan produk yang dimasukkan.
- **product_in_item.batch_id → stock_batch.id**
  Menghubungkan ke batch yang dipakai/dibuat (nullable saat entry baru).

### 3.2 Produk Keluar

- **product_out_item.product_out_id → product_out.id**
  Detail keluar terkait ke transaksi keluar tertentu (one-to-many).
- **product_out_item.batch_id → stock_batch.id**
  Stok yang diambil berasal dari batch tertentu (many-to-one).

### 3.3 Produk Retur

- **product_return_item.product_return_id → product_return.id**
  Detail retur yang terkait ke satu transaksi retur.
- **product_return_item.product_id → product.id**
  Produk yang diretur dicatat per item.
- **(Tidak ada batch_id langsung di retur item)**
  Tapi saat retur berhasil masuk stok, akan mengarah ke batch yang sesuai di `stock_batch`.

### 3.4 Stok Opname

- **stock_opname_item.opname_id → stock_opname.id**
  Detail opname per transaksi.
- **stock_opname_item.batch_id → stock_batch.id**
  Cek stok dilakukan per batch.
