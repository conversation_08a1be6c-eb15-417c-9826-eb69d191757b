# Dokumentasi Implementasi Fitur Lightbox

## 📋 Deskripsi

Fitur lightbox sederhana untuk melihat foto bukti dalam ukuran penuh dengan kemampuan zoom. Terintegrasi dengan sistem foto bukti pada produk masuk dan produk keluar.

## 🎯 Fitur Utama

- ✅ Click foto untuk zoom
- ✅ Support multiple foto tanpa batasan
- ✅ Section collapsed by default dan collapsible
- ✅ Keyboard shortcuts (ESC untuk close)
- ✅ Click outside untuk close
- ✅ Smooth transitions
- ✅ Responsive design
- ✅ Auto-detection untuk dynamic content (Filament SPA)

## 📍 Lokasi Implementasi

### Detail View Produk Masuk
- **Route**: `/admin/product-ins/{id}`
- **File**: `app/Filament/Resources/ProductInResource.php`
- **Section**: "Foto Bukti" (collapsed by default)

### Detail View Produk Keluar
- **Route**: `/admin/product-outs/{id}`
- **File**: `app/Filament/Resources/ProductOutResource.php`
- **Section**: "Foto Bukti" (collapsed by default)

## 🔧 Komponen Teknis

### 1. Frontend Assets

#### CSS (`public/css/lightbox.css`)
```css
.lightbox-overlay          // Overlay background gelap
.lightbox-content          // Container untuk foto
.lightbox-image           // Foto yang di-zoom
.lightbox-close           // Tombol close (X)
.lightbox-image-gallery   // Class untuk gallery container
```

#### JavaScript (`public/js/lightbox.js`)
- Event handling untuk click foto
- Lightbox overlay management
- Keyboard shortcuts (ESC)
- Dynamic content detection (untuk Filament SPA)
- Auto-attach events untuk foto baru

### 2. Backend Integration

#### AdminPanelProvider
```php
// app/Providers/Filament/AdminPanelProvider.php
FilamentView::registerRenderHook(
    PanelsRenderHook::STYLES_AFTER,
    fn (): string => Blade::render('<link rel="stylesheet" href="{{ asset(\'css/lightbox.css\') }}">')
);

FilamentView::registerRenderHook(
    PanelsRenderHook::SCRIPTS_AFTER,
    fn (): string => Blade::render('<script src="{{ asset(\'js/lightbox.js\') }}"></script>')
);
```

#### ImageEntry Configuration
```php
ImageEntry::make('evidence_photos')
    ->label('Foto Bukti')
    ->disk('public')
    ->height(200)
    ->width(300)
    ->columnSpanFull()
    ->stacked()
    ->extraAttributes([
        'class' => 'lightbox-image-gallery',
        'style' => 'cursor: pointer;'
    ])
    ->visible(fn ($record) => !empty($record->evidence_photos))
```

#### Section Configuration
```php
Section::make('Foto Bukti')
    ->schema([...])
    ->visible(fn ($record) => !empty($record->evidence_photos))
    ->collapsed()      // Default tertutup
    ->collapsible()    // Bisa dibuka/tutup
```

## 🚀 Cara Penggunaan

### Untuk User
1. Buka detail view produk masuk atau produk keluar
2. Cari section "Foto Bukti" (tertutup secara default)
3. Click header section untuk membuka dan melihat foto-foto
4. Click foto yang ingin di-zoom
5. Tutup lightbox dengan tombol X, click outside, atau ESC

### Untuk Developer
```php
// Menambahkan lightbox ke ImageEntry lain
ImageEntry::make('field_name')
    ->extraAttributes([
        'class' => 'lightbox-image-gallery',
        'style' => 'cursor: pointer;'
    ])
```

## 🔧 File yang Dimodifikasi

1. **app/Filament/Resources/ProductInResource.php**
   - Tambah `lightbox-image-gallery` class
   - Tambah `cursor: pointer` style
   - Hapus `->limit(3)` dan `->limitedRemainingText()`
   - Tambah `->collapsed()` dan `->collapsible()`

2. **app/Filament/Resources/ProductOutResource.php**
   - Tambah `lightbox-image-gallery` class
   - Tambah `cursor: pointer` style
   - Hapus `->limit(3)` dan `->limitedRemainingText()`
   - Tambah `->collapsed()` dan `->collapsible()`

3. **public/css/lightbox.css** (baru)
   - Styling untuk lightbox overlay dan components

4. **public/js/lightbox.js** (baru)
   - JavaScript functionality untuk lightbox

5. **app/Providers/Filament/AdminPanelProvider.php**
   - Asset registration dengan FilamentView hooks

## 🎨 Fitur UI/UX

- **Cursor pointer** pada foto untuk menunjukkan clickable
- **Hover effect** - foto sedikit membesar saat di-hover
- **Smooth transitions** - animasi fade in/out yang halus
- **Responsive design** - menyesuaikan ukuran layar
- **Default collapsed** - section foto tertutup saat page load
- **Collapsible** - bisa dibuka/tutup dengan click header

## 🔒 Keamanan & Performance

- Foto disimpan di `storage/app/public/` dengan disk 'public'
- Lazy loading - foto tidak di-render sampai section dibuka
- Optimized images - menggunakan CompressedFileUpload component
- Minimal JavaScript - vanilla JS tanpa library tambahan

## 🐛 Troubleshooting

### Lightbox Tidak Muncul
1. Pastikan assets sudah di-build: `npm run build`
2. Check browser console untuk error JavaScript
3. Pastikan file CSS dan JS ada di `public/css/` dan `public/js/`

### Foto Tidak Clickable
1. Pastikan class `lightbox-image-gallery` ada pada ImageEntry
2. Check apakah JavaScript sudah loaded
3. Pastikan foto sudah ter-render di DOM

## 📝 Implementation Notes

- Menggunakan vanilla JavaScript untuk kompatibilitas maksimal
- CSS menggunakan modern properties dengan fallback
- Auto-detection untuk dynamic content menggunakan MutationObserver
- Section collapsible menggunakan Filament built-in functionality
- Assets di-load melalui Filament render hooks untuk integrasi yang baik

## 🔮 Future Enhancements

- Navigation arrows untuk multiple foto
- Zoom in/out controls
- Download foto functionality
- Touch gestures untuk mobile
- Image preloading
