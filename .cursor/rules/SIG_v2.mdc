---
description: 
globs: 
alwaysApply: true
---
# SIG v2

## Master Data

### Produk

- <PERSON><PERSON>
- Brand
- SKU
- Harga Beli Default
- <PERSON><PERSON> Jual Default

### Brand

- Nama Brand

### <PERSON><PERSON><PERSON>

- <PERSON><PERSON>
- Role

### Supplier

- Nama Supplier

### Sales Channel

- Nama Sales Channel

## Alur Sistem

### Produk Masuk

1. View List → Kolom Tabel
   1. Tanggal Masuk
   2. Supplier
   3. Jumlah Produk
   4. Total Kuantitas
2. View Create → Isian Form
   1. Tanggal Masuk
   2. Pilih Supplier
   3. Catatan (opsional)
   4. Repeater:
      1. Pilih Produk
      2. Ju<PERSON>lah Masuk
      3. Tanggal kedaluwarsa
      4. Harga Beli
3. View Detail
   1. Tanggal Masuk
   2. Supplier
   3. Catatan
   4. Repeater → Tabel:
      1. Produk
      2. Brand
      3. Jumlah Masuk
      4. Tanggal kedaluwarsa
      5. Harga Beli
4. Logic
   1. Stok produk masuk dibedakan berdasarkan tanggal kedaluwarsa dan harga beli nya, jika sudah ada produk, tanggal kedal<PERSON><PERSON><PERSON>, dan harga yang sama di database maka stok akan ditambah, jika belum ada maka akan membuat record stok baru.
   2. setiap kali ada produk masuk maka pergerakan stoknya akan dicatat sebagai "IN" dengan deskripsi "IN #[transaction_id] at [movement_date] from [supplier_name]"
   3. catat nama user yang melakukan produk masuk

### Produk Keluar

1. View List → Tabel
   1. Tanggal Keluar
   2. Sales Channel
   3. Jumlah Produk
   4. Total Kuantitas
2. Create → Isian Form
   1. Tanggal Keluar
   2. Pilihan Sales Channel
   3. Catatan (opsional)
   4. Repeater:
      1. Pilih Produk
      2. Jumlah Keluar
      3. Harga Jual
      4. Diskon (opsional)
3. View Detail
   1. Tanggal Keluar
   2. Pilihan Sales Channel
   3. Catatan
   4. Repeater → Tabel:
      1. Produk
      2. Brand
      3. Tanggal kedaluwarsa
      4. Jumlah Keluar
      5. Harga Jual
      6. Diskon (opsional)
4. Logic
   1. Ketika produk dikeluarkan, stok yang dikeluarkan terlebih dahulu adalah stok dengan tanggal kedaluwarsa paling dekat dari tanggal produk keluar (First Expired First Out) dan harga yang paling rendah (Landed Cost Optimization).
   2. Jika jumlah keluar melebihi stok pada tanggal kedaluwarsa paling dekat maka habiskan terlebih dahulu stok pada tanggal kedaluwarsa paling dekat, baru ambil sisanya dari stok tanggal kedaluwarsa terdekat setelahnya.
   3. setiap kali ada produk keluar maka pergerakan stoknya akan dicatat sebagai "OUT" dengan deskripsi "Out #[transaction_id] at [movement_date] to [channel_name]"
   4. menampilkan tanggal kedaluwarsa yang dipilih beserta jumlahnya untuk stok keluar pada halaman detail berdasarkan poin pertama.
   5. catat nama user yang melakukan produk keluar

### Produk Retur

1. View List → tabel
   1. Tanggal retur
   2. Sales channel
   3. Jumlah produk
   4. Total kuantitas
2. Create → Isian Form
   1. Tanggal retur
   2. Pilihan sales channel
   3. Catatan (opsional)
   4. Repeater:
      1. Pilih produk
      2. Tanggal kedaluwarsa
      3. Jumlah produk
      4. Kondisi (baik/buruk)
3. List Detail
   1. Tanggal retur
   2. Sales channel
   3. Catatan
   4. Repeater:
      1. Produk
      2. Brand
      3. Tanggal kedaluwarsa
      4. Jumlah produk
      5. Kondisi(baik/buruk)
4. Logic
   1. Produk retur dengan kondisi baik dan tanggal kedaluwarsanya ada di dalam database maka akan stok akan bertambah, dan otomatis diberikan harga paling rendah di tanggal kedaluwarsa yang sama. jika tanggal kedaluwarsanya tidak ada di dalam database maka munculkan error produk dengan tanggal kedaluwarsa tersebut belum ada sebelumnya di sistem.
   2. setiap kali produk retur berhasil menambah stok yang ada di database maka pergerakan stoknya akan dicatat sebagai "RETURN" dengan deskripsi "RETURN #[transaction_id] at [movement_date] from [channel_name]"
   3. Produk retur dengan kondisi buruk maka akan tercatat sebagai produk reject
   4. catat nama user yang melakukan produk retur

### Stok Opname

1. View List → Tabel
   1. Tanggal Opname
   2. Catatan
2. Create → Isian Form:
   1. Tanggal Opname
   2. Catatan (opsional)
   3. Repeater:
      1. Pilih Produk
      2. Pilih Tanggal Kedaluwarsa
      3. Pilih Harga Beli
      4. Stok Sistem (bukan input)
      5. Stok Aktual
3. View Detail
   1. Tanggal Opname
   2. Catatan (opsional)
   3. Repeater → Tabel:
      1. Produk
      2. Tanggal Kedaluwarsa
      3. Harga Beli
      4. Stok Sistem (bukan input)
      5. Stok Aktual
      6. Perbedaan (+/-)
4. Logic
   1. Merubah stok di sistem sesuai dengan stok aktual
   2. setiap kali ada opname di produk, tanggal kedaluwarsa, dan harga beli tersebut maka pergerakan stoknya akan dicatat sebagai "OPNAME" dengan deskripsi "OPNAME #[transaction_id] at [movement_date]"

### Cek Stok

1. View List → Tabel
   1. Produk
   2. Brand
   3. Stok Total
2. View Detail
   1. Produk
   2. Brand
   3. Stok Total
   4. Repeater → Tabel:
      1. Tanggal Kedaluwarsa
      2. Harga Beli
      3. Jumlah Stok
      4. Action → Lihat History Pergerakan → Tabel :
         1. Tipe Pergerakan (IN, OUT, RETURN, OPNAME)
         2. Tanggal Pergerakan
         3. Jumlah stok(berkurang atau bertambah)
         4. stok setelah pengurangan/penambahan

### Laporan Stok

1. View List → Tabel
   1. Tanggal Transaksi
   2. Nama Produk
   3. Tanggal Kedaluwarsa
   4. Jenis Transaksi (Masuk, Keluar, Retur, Opname)
   5. Jumlah (+ jika masuk, - jika keluar)
   6. Stok
   7. Harga Beli
   8. Harga Jual (terisi jika jenis transaksi Keluar)
   9. nilai stok
   10. Keterangan (ambil dari deskripsi movement)

### Laporan Keuangan

#### 1. Laporan Penjualan (Sales Report)

1.  **Tujuan:** Memberikan gambaran detail mengenai pendapatan dari penjualan produk.
2.  **Data Sumber:** Transaksi `Product Out`.
3.  **Metrik Utama:**
    *   Total Pendapatan Kotor (Quantity \* Harga Jual)
    *   Total Diskon
    *   Total Pendapatan Bersih (Pendapatan Kotor - Diskon)
    *   Jumlah Produk Terjual
    *   Rata-rata Harga Jual
4.  **Pengelompokan/Filter:** Berdasarkan periode waktu (harian, mingguan, bulanan), `Sales Channel`, `Produk`, `Brand`.

#### 2. Laporan Harga Pokok Penjualan (HPP / COGS Report)

1.  **Tujuan:** Menghitung biaya langsung yang terkait dengan produk yang terjual.
2.  **Data Sumber:** Transaksi `Product Out` dan data `Stock Batch` yang terkait (untuk mendapatkan `Harga Beli` dari batch yang keluar). `Stock Movement` tipe "OUT" harus menyimpan informasi harga beli batch yang digunakan.
3.  **Metrik Utama:**
    *   Total HPP (Jumlah kuantitas keluar \* Harga Beli batch yang sesuai)
4.  **Pengelompokan/Filter:** Berdasarkan periode waktu, `Produk`, `Brand`, `Sales Channel`.

#### 3. Laporan Laba Kotor (Gross Profit Report)

1.  **Tujuan:** Menunjukkan profitabilitas penjualan produk sebelum memperhitungkan biaya operasional lain.
2.  **Data Sumber:** Hasil dari Laporan Penjualan dan Laporan HPP.
3.  **Metrik Utama:**
    *   Pendapatan Bersih
    *   Total HPP
    *   Laba Kotor (Pendapatan Bersih - Total HPP)
    *   Margin Laba Kotor (%) ((Laba Kotor / Pendapatan Bersih) \* 100%)
4.  **Pengelompokan/Filter:** Berdasarkan periode waktu, `Produk`, `Brand`, `Sales Channel`.

#### 4. Laporan Nilai Inventaris (Inventory Valuation Report)

1.  **Tujuan:** Menampilkan nilai total dari stok yang dimiliki pada waktu tertentu.
2.  **Data Sumber:** Data `Stock Batch` (Stok Tersisa \* Harga Beli).
3.  **Metrik Utama:**
    *   Total Nilai Inventaris
    *   Nilai Inventaris per `Produk`
    *   Nilai Inventaris per `Brand`
    *   Detail per Batch (Produk, Tanggal Kedaluwarsa, Harga Beli, Stok, Nilai Batch)
4.  **Pengelompokan/Filter:** Berdasarkan tanggal snapshot, `Produk`, `Brand`.

#### 5. Laporan Pembelian (Purchase Report)

1.  **Tujuan:** Memberikan ringkasan transaksi pembelian dari supplier.
2.  **Data Sumber:** Transaksi `Product In`.
3.  **Metrik Utama:**
    *   Total Biaya Pembelian (Quantity \* Harga Beli)
    *   Total Kuantitas Dibeli
    *   Rata-rata Harga Beli
4.  **Pengelompokan/Filter:** Berdasarkan periode waktu, `Supplier`, `Produk`.
