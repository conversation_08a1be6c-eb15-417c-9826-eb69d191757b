# Integrasi Saluran Penjualan dengan Sistem Diskon

## Overview

Sistem diskon telah diupdate untuk mendukung pilihan saluran penjualan. Diskon dapat dikonfigurasi untuk berlaku pada saluran penjualan tertentu atau semua saluran penjualan.

## Perubahan Database

### Tabel `discounts`

-   **Field baru**: `sales_channel_id` (nullable, foreign key ke `sales_channels.id`)
-   **Logika**:
    -   Jika `sales_channel_id` = NULL → diskon berlaku untuk semua saluran penjualan
    -   Jika `sales_channel_id` = ID tertentu → diskon hanya berlaku untuk saluran penjualan tersebut

## Perubahan Model

### Model `Discount`

-   Menambah relasi `salesChannel()` ke model `SalesChannel`
-   Menambah `sales_channel_id` ke `$fillable`

## Perubahan Form Diskon

### Form Create/Edit Diskon

-   Menambah field `Select` untuk memilih saluran penjualan
-   Field bersifat optional (dapat dikosongkan untuk semua saluran)
-   Menggunakan searchable dropdown dengan data dari tabel `sales_channels`

### Tabel Diskon

-   Menambah kolom "Saluran Penjualan" yang menampilkan nama saluran atau "Semua Saluran"
-   Menambah filter berdasarkan saluran penjualan

### View Diskon

-   Menampilkan informasi saluran penjualan di halaman detail diskon

## Perubahan DiscountService

### Method `getActiveDiscountForProduct()`

-   **Parameter baru**: `$salesChannelId` (optional)
-   **Logika**: Mencari diskon yang:
    1. Sedang aktif (berdasarkan tanggal)
    2. Berlaku untuk produk tertentu
    3. Berlaku untuk saluran penjualan yang dipilih ATAU berlaku untuk semua saluran (sales_channel_id = NULL)

### Method `getDiscountedPrice()`

-   **Parameter baru**: `$salesChannelId` (optional)
-   Menggunakan `getActiveDiscountForProduct()` dengan parameter saluran penjualan

### Method `hasActiveDiscount()`

-   **Parameter baru**: `$salesChannelId` (optional)
-   Konsisten dengan method lainnya

### Method `getAllActiveDiscountsForProduct()`

-   **Parameter baru**: `$salesChannelId` (optional)
-   Menggunakan logika filter yang sama

## Perubahan Form Produk Keluar

### Auto Discount Integration

-   Form produk keluar sekarang mengirimkan `channel_id` ke `DiscountService`
-   Auto diskon akan mempertimbangkan saluran penjualan yang dipilih
-   Ketika saluran penjualan berubah, semua auto diskon akan diupdate secara otomatis

### Reaktivitas Form

-   Field `channel_id` menggunakan `live()` dan `afterStateUpdated()`
-   Ketika saluran penjualan berubah, sistem akan:
    1. Loop melalui semua items dalam form
    2. Update auto diskon untuk setiap item yang tidak menggunakan diskon manual
    3. Set ulang `discount_id` dan `final_price` berdasarkan diskon yang berlaku

## Logika Prioritas Diskon

1. **Prioritas Tanggal** → Jika ada multiple diskon, diprioritaskan berdasarkan tanggal berakhir terdekat
2. **Diskon Spesifik Saluran** → Diskon yang memiliki `sales_channel_id` sesuai dengan saluran yang dipilih
3. **Diskon Umum** → Diskon yang memiliki `sales_channel_id` = NULL (berlaku untuk semua saluran)

## Contoh Penggunaan

### Skenario 1: Diskon untuk Saluran Tertentu

-   Buat diskon dengan nama "Diskon Tokopedia 10%"
-   Pilih saluran penjualan "Tokopedia"
-   Diskon hanya akan berlaku untuk transaksi produk keluar dengan saluran "Tokopedia"

### Skenario 2: Diskon untuk Semua Saluran

-   Buat diskon dengan nama "Diskon Ramadan 15%"
-   Kosongkan pilihan saluran penjualan
-   Diskon akan berlaku untuk semua saluran penjualan

### Skenario 3: Multiple Diskon

-   Produk A memiliki:
    -   Diskon umum 10% (semua saluran)
    -   Diskon khusus Shopee 15%
-   Ketika memilih saluran Shopee → akan menggunakan diskon 15%
-   Ketika memilih saluran lain → akan menggunakan diskon 10%

## Migration

```php
// 2025_06_08_000000_add_sales_channel_id_to_discounts_table.php
Schema::table('discounts', function (Blueprint $table) {
    $table->foreignId('sales_channel_id')
          ->nullable()
          ->after('name')
          ->constrained('sales_channels')
          ->onDelete('cascade')
          ->comment('Saluran penjualan (nullable untuk semua saluran)');
});
```

## Backward Compatibility

-   Diskon yang sudah ada akan memiliki `sales_channel_id` = NULL
-   Diskon lama akan tetap berlaku untuk semua saluran penjualan
-   Tidak ada perubahan pada data existing
