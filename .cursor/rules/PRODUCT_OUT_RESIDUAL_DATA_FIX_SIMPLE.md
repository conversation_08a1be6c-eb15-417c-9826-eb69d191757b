# Perbaikan Data Residu pada Sistem Produk Keluar - <PERSON><PERSON><PERSON>

## Masalah yang Diperbaiki

Setelah produk keluar disetujui, masih ada data residu yang ditampilkan di relation manager. Data residu ini adalah produk-produk yang sebelumnya ditampilkan saat status masih pending approval (dimana `batch_id` masih `null` karena stock belum diproses).

## Penyebab Masalah

### Perbedaan Implementasi ProductIn vs ProductOut

**ProductIn (Tidak ada masalah residu):**

1. **Saat create**: ProductInItem dibuat dengan `batch_id = null`
2. **Saat approved**: `ProductInStockService` membuat StockBatch baru dan **mengupdate item original** dengan `batch_id`
3. **Hasil**: Tidak ada data residu karena item original diupdate

**ProductOut (Ada masalah residu):**

1. **Saat create**: ProductOutItem dibuat dengan `batch_id = null`
2. **Saat approved**: `ProductOutService` **membuat ProductOutItem BARU** dengan `batch_id`
3. **Hasil**: Item original dengan `batch_id = null` tetap ada → **DATA RESIDU**

## Solusi Sederhana: Ikuti Pola ProductIn

Daripada membuat sistem filtering yang kompleks, solusi yang lebih sederhana adalah mengikuti pola yang sama dengan ProductIn: **update item original** daripada membuat item baru.

### Perbaikan di ProductOutService

**File:** `app/Services/ProductOutService.php`

#### A. Perbaikan untuk Normal Stock

**Sebelumnya (membuat item baru):**

```php
// 4. Buat ProductOutItem untuk batch ini
$outItem = new ProductOutItem();
$outItem->product_out_id = $productOut->id;
$outItem->batch_id = $batch->id;
// ... set properties lainnya
$outItem->save();
```

**Sekarang (update item existing):**

```php
// 4. Update ProductOutItem yang sudah ada dengan batch_id
// Cari item yang sesuai dengan product_id dan belum memiliki batch_id
$existingItem = $productOut->items()
    ->where('product_id', $productId)
    ->whereNull('batch_id')
    ->where('stock_condition', 'good')
    ->first();

if ($existingItem) {
    $existingItem->batch_id = $batch->id;
    $existingItem->save();
} else {
    // Fallback: buat item baru jika tidak ditemukan
    $outItem = new ProductOutItem();
    $outItem->product_out_id = $productOut->id;
    $outItem->batch_id = $batch->id;
    $outItem->quantity = $quantityFromThisBatch;
    $outItem->sale_price = $item['sale_price'];
    $outItem->final_price = !empty($item['final_price']) ? $item['final_price'] : null;
    $outItem->discount_id = !empty($item['discount_id']) ? $item['discount_id'] : null;
    $outItem->is_manual_discount = isset($item['is_manual_discount']) ? (bool)$item['is_manual_discount'] : false;
    $outItem->stock_condition = 'good';
    $outItem->save();
}
```

#### B. Perbaikan untuk Bad Stock

Penerapan logika yang sama untuk bad stock:

```php
// Update ProductOutItem yang sudah ada dengan batch_id
$existingItem = $productOut->items()
    ->where('product_id', $productId)
    ->whereNull('batch_id')
    ->where('stock_condition', 'bad')
    ->first();

if ($existingItem) {
    $existingItem->batch_id = $batch->id;
    $existingItem->save();
} else {
    // Fallback: buat item baru jika tidak ditemukan
    // ... kode fallback yang sama
}
```

#### C. Perbaikan untuk Package Items

**Masalah Package:**

-   ProductIn: Menghapus item paket original setelah breakdown ke komponen
-   ProductOut: Tidak menghapus item paket original → **RESIDU PAKET**

**Solusi:**

```php
// Process package items
if (!empty($packageItems)) {
    $results[] = $this->packageStockService->processPackageStockOut($productOut, $packageItems);

    // Hapus item paket original setelah diproses (sama seperti ProductIn)
    // Untuk package items, product_id di database adalah null dan package_product_id berisi ID paket
    $packageProductIds = array_column($packageItems, 'product_id');
    $productOut->items()
        ->whereIn('package_product_id', $packageProductIds)
        ->whereNull('batch_id') // Hanya hapus yang belum diproses
        ->delete();
}
```

**Penjelasan:**

-   Package items di database: `product_id = null`, `package_product_id = ID_paket`
-   Saat identifikasi: menggunakan `$item->package_product_id`
-   Saat penghapusan: harus menggunakan `whereIn('package_product_id', ...)` bukan `product_id`

## Hasil Perbaikan

1. **✅ Tidak Ada Data Residu Produk Satuan**: Item original diupdate dengan `batch_id`
2. **✅ Tidak Ada Data Residu Package**: Item paket original dihapus setelah breakdown
3. **✅ Konsistensi dengan ProductIn**: Menggunakan pola yang sama dengan sistem produk masuk
4. **✅ Solusi Sederhana**: Tidak perlu filtering kompleks atau UI tambahan
5. **✅ Backward Compatibility**: Fallback tetap tersedia jika item tidak ditemukan

## Keuntungan Pendekatan Ini

1. **Sederhana**: Hanya perubahan minimal di service layer
2. **Konsisten**: Mengikuti pola yang sudah terbukti bekerja di ProductIn
3. **Maintainable**: Tidak menambah kompleksitas di UI layer
4. **Reliable**: Mengatasi akar masalah, bukan hanya menyembunyikan gejala

## Perbandingan Pendekatan

| Aspek                | Pendekatan Kompleks (Sebelumnya)  | Pendekatan Sederhana (Sekarang) |
| -------------------- | --------------------------------- | ------------------------------- |
| **Perubahan Code**   | Model + RelationManager + Filter  | Hanya Service                   |
| **Kompleksitas**     | Tinggi (UI filtering, badge, dll) | Rendah (update/delete existing) |
| **Maintenance**      | Sulit (banyak komponen)           | Mudah (satu tempat)             |
| **Konsistensi**      | Berbeda dengan ProductIn          | Sama dengan ProductIn           |
| **User Experience**  | Perlu filter manual               | Otomatis bersih                 |
| **Package Handling** | Residu tetap ada                  | Dihapus seperti ProductIn       |

Dengan perbaikan ini, masalah data residu telah teratasi secara fundamental dengan mengikuti pola yang sudah ada dan terbukti bekerja dengan baik di sistem produk masuk.
