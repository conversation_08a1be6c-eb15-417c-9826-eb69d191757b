# Implementasi Pembagian Harga Paket

## Deskripsi
Implementasi fitur pembagian harga paket (beli dan jual) secara sama rata ke semua komponen produk ketika paket masuk dan keluar dari sistem inventory.

## Perubahan yang Dilakukan

### 1. PackageStockService.php
**File:** `app/Services/PackageStockService.php`

#### A. Perubahan pada method `processPackageStockIn()` (Produk Masuk):
- Menambahkan perhitungan total jumlah komponen dalam paket
- Menghitung harga beli per unit komponen dengan pembagian sama rata
- Menggunakan harga per unit komponen untuk setiap komponen saat membuat stock batch

**Kode yang ditambahkan:**
```php
// Hitung total jumlah komponen dalam paket untuk pembagian harga
$totalComponentQuantity = $compositions->sum('quantity');

// Hitung harga beli per unit komponen (pembagian sama rata)
$componentPurchasePrice = $totalComponentQuantity > 0
    ? $packagePurchasePrice / $totalComponentQuantity
    : 0;
```

#### B. Perubahan pada method `processPackageStockOut()` (Produk Keluar):
- Menambahkan perhitungan total jumlah komponen dalam paket
- Menghitung harga jual per unit komponen dengan pembagian sama rata
- Menggunakan harga jual per unit komponen untuk setiap komponen saat membuat ProductOutItem

**Kode yang ditambahkan:**
```php
// Hitung total jumlah komponen dalam paket untuk pembagian harga jual
$totalComponentQuantity = $compositions->sum('quantity');

// Hitung harga jual per unit komponen (pembagian sama rata)
$componentSalePrice = $totalComponentQuantity > 0
    ? $packageSalePrice / $totalComponentQuantity
    : 0;
```

#### C. Perubahan pada method `deductNormalStock()` dan `deductBadStock()`:
- Mengubah signature method untuk menerima `$salePrice` dan `$discount` terpisah
- Menggunakan harga jual per komponen yang sudah dihitung untuk ProductOutItem

**Signature method baru:**
```php
private function deductNormalStock(
    int $componentId,
    int $quantity,
    ProductOut $productOut,
    Product $package,
    Product $component,
    float $salePrice,    // Parameter baru
    float $discount      // Parameter baru
): void

private function deductBadStock(
    int $componentId,
    int $quantity,
    ProductOut $productOut,
    Product $package,
    Product $component,
    float $salePrice,    // Parameter baru
    float $discount      // Parameter baru
): void
```

### 2. BadStockService.php
**File:** `app/Services/BadStockService.php`

**Perubahan pada method `addBadStock()`:**
- Menambahkan parameter `?float $purchasePrice` untuk mendukung harga beli spesifik
- Menambahkan logika untuk mencari atau membuat batch dengan harga beli yang tepat
- Mempertahankan backward compatibility dengan behavior lama

**Signature method baru:**
```php
public function addBadStock(
    int $productId,
    string $expirationDate,
    ?float $purchasePrice,  // Parameter baru
    int $quantity,
    int $userId,
    string $description,
    string $movementDate
): StockBatch
```

### 3. CreateProductReturn.php
**File:** `app/Filament/Resources/ProductReturnResource/Pages/CreateProductReturn.php`

**Perubahan:**
- Memperbaiki pemanggilan method `addBadStock()` dengan menambahkan parameter `null` untuk purchase price
- Mempertahankan behavior lama untuk product return (menggunakan batch dengan harga terendah)

## Cara Kerja

### Contoh Skenario Produk Masuk
**Paket A** berisi:
- 2 unit Produk B
- 3 unit Produk C
- Harga beli paket: Rp 100,000

**Perhitungan Harga Beli:**
1. Total komponen = 2 + 3 = 5 unit
2. Harga beli per unit = Rp 100,000 ÷ 5 = Rp 20,000
3. Produk B mendapat harga beli: Rp 20,000 per unit
4. Produk C mendapat harga beli: Rp 20,000 per unit

**Hasil Produk Masuk:**
- Produk B: 2 × Rp 20,000 = Rp 40,000
- Produk C: 3 × Rp 20,000 = Rp 60,000
- **Total: Rp 100,000** (sama dengan harga beli paket asli)

### Contoh Skenario Produk Keluar
**Paket A** yang sama dijual dengan:
- Harga jual paket: Rp 150,000
- Diskon paket: 5%

**Perhitungan Harga Jual:**
1. Total komponen = 2 + 3 = 5 unit
2. Harga jual per unit = Rp 150,000 ÷ 5 = Rp 30,000
3. Produk B mendapat harga jual: Rp 30,000 per unit
4. Produk C mendapat harga jual: Rp 30,000 per unit
5. Diskon 5% diterapkan ke semua komponen

**Hasil Produk Keluar:**
- Produk B: 2 × Rp 30,000 = Rp 60,000 (diskon 5% = Rp 3,000)
- Produk C: 3 × Rp 30,000 = Rp 90,000 (diskon 5% = Rp 4,500)
- **Total sebelum diskon: Rp 150,000**
- **Total setelah diskon: Rp 142,500** (sama dengan harga jual paket setelah diskon)

## Keuntungan

1. **Pembagian Adil**: Setiap unit komponen mendapat harga yang sama (beli dan jual)
2. **Akurasi Finansial**: Total harga komponen = harga paket asli (untuk masuk dan keluar)
3. **Konsistensi**: Harga komponen konsisten untuk analisis cost dan profit
4. **Backward Compatibility**: Tidak merusak fungsionalitas yang sudah ada
5. **Transparansi**: Harga jual komponen mencerminkan proporsi yang adil dari harga paket

## Testing

Implementasi telah diverifikasi dengan:
- ✅ Test perhitungan matematika pembagian harga beli dan jual
- ✅ Verifikasi tidak ada selisih antara total komponen dan harga paket
- ✅ Kompatibilitas dengan sistem bad stock dan normal stock
- ✅ Backward compatibility dengan product return
- ✅ Test pembagian harga jual dengan diskon paket

## Dampak pada Sistem

### Stock Batch (Produk Masuk)
- Komponen paket akan memiliki stock batch dengan harga beli yang dihitung dari pembagian paket
- Setiap komponen dengan kondisi yang sama akan masuk ke batch yang sesuai

### ProductOutItem (Produk Keluar)
- Komponen paket akan memiliki ProductOutItem dengan harga jual yang dihitung dari pembagian paket
- Diskon paket akan diterapkan secara proporsional ke semua komponen

### Bad Stock
- Komponen dengan kondisi 'bad' akan masuk ke bad stock dengan harga beli yang sudah dibagi
- Menggunakan batch dengan harga beli yang tepat atau membuat batch baru jika diperlukan

### Reporting
- Laporan cost akan lebih akurat karena harga beli komponen mencerminkan cost sebenarnya
- Laporan sales akan lebih akurat karena harga jual komponen mencerminkan revenue sebenarnya
- Analisis profitability per produk menjadi lebih presisi
- Margin analysis menjadi lebih akurat dengan pembagian harga yang konsisten

## Catatan Implementasi

1. **Pembagian Sama Rata**: Semua komponen mendapat harga per unit yang sama, terlepas dari jenis produknya
2. **Handling Zero Division**: Ada proteksi untuk kasus di mana total komponen = 0
3. **Precision**: Menggunakan float untuk menghindari kehilangan presisi dalam pembagian
4. **Transaction Safety**: Semua operasi dilakukan dalam database transaction
5. **Diskon Proporsional**: Diskon paket diterapkan dengan persentase yang sama ke semua komponen
6. **FEFO Compatibility**: Pembagian harga tidak mempengaruhi logika FEFO yang sudah ada

## File yang Terpengaruh

1. `app/Services/PackageStockService.php` - Logic utama pembagian harga (masuk dan keluar)
2. `app/Services/BadStockService.php` - Support untuk harga beli spesifik
3. `app/Filament/Resources/ProductReturnResource/Pages/CreateProductReturn.php` - Compatibility fix

## Summary

Implementasi ini memastikan bahwa:
- **Produk Masuk**: Harga beli paket dibagi sama rata ke semua komponen
- **Produk Keluar**: Harga jual paket dibagi sama rata ke semua komponen
- **Konsistensi**: Pembagian harga dilakukan dengan logika yang sama untuk masuk dan keluar
- **Akurasi**: Total harga komponen selalu sama dengan harga paket asli
- **Transparansi**: Setiap komponen memiliki harga yang mencerminkan kontribusinya dalam paket
