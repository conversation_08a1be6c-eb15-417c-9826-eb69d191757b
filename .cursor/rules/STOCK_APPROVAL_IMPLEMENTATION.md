# Implementasi Logika Bisnis Stock Batches dengan Approval System

## Overview
Implementasi ini memastikan bahwa stock batches tidak akan bertambah atau ter-update di database sampai produk masuk (product in) mendapat persetujuan penuh dari store manager dan owner sesuai dengan sistem approval multi-level yang sudah ada.

## Perubahan yang Dilakukan

### 1. ProductInObserver (Baru)
**File:** `app/Observers/ProductInObserver.php`

Observer ini menangani perubahan status pada ProductIn model:
- **Status berubah ke APPROVED**: Memproses stock batches secara otomatis
- **Status berubah ke REJECTED**: Menghapus stock batches jika ada
- **Model dihapus**: Membersihkan stock batches sebelum penghapusan

### 2. ProductInStockService (Baru)
**File:** `app/Services/ProductInStockService.php`

Service yang menangani semua operasi stock batches untuk produk masuk:
- `processApprovedProductIn()`: Memproses stock batches untuk produk yang sudah approved
- `processNewProductIn()`: Memproses produk baru (hanya jika langsung approved oleh owner)
- `removeStockBatches()`: Menghapus stock batches untuk produk yang ditolak
- `reprocessStockBatches()`: Memproses ulang stock batches saat edit (hanya jika approved)
- `hasProcessedStockBatches()`: Mengecek apakah produk sudah memiliki stock batches

### 3. Modifikasi CreateProductIn
**File:** `app/Filament/Resources/ProductInResource/Pages/CreateProductIn.php`

Perubahan:
- Menghapus pemrosesan stock batch langsung di `afterCreate()`
- Menggunakan `ProductInStockService::processNewProductIn()` yang hanya memproses jika langsung approved
- Stock batches untuk pending approval akan diproses oleh Observer saat status berubah

### 4. Modifikasi EditProductIn
**File:** `app/Filament/Resources/ProductInResource/Pages/EditProductIn.php`

Perubahan:
- Menambahkan pengecekan status approval sebelum reprocess stock batches
- Menggunakan `ProductInStockService::reprocessStockBatches()` yang memiliki pengecekan approval
- Menyederhanakan method `reprocessStockBatches()` untuk hanya menangani items

### 5. Update Approval Actions
**File:** `app/Filament/Resources/ProductInResource/Pages/ViewProductIn.php`
**File:** `app/Filament/Resources/ProductInResource/Pages/ApprovalProductIn.php`

Perubahan:
- Menambahkan notifikasi bahwa stock batches sedang diproses saat approval
- Menambahkan notifikasi bahwa stock batches tidak akan diproses saat rejection
- Stock processing dilakukan otomatis oleh Observer

### 6. Registrasi Observer
**File:** `app/Providers/AppServiceProvider.php`

Menambahkan registrasi ProductInObserver di method `boot()`.

## Alur Kerja Sistem

### Skenario 1: Admin Packing Membuat Produk Masuk
1. Admin packing membuat record dengan status `WAITING_STORE_MANAGER_APPROVAL`
2. Stock batches **TIDAK** diproses
3. Record tersimpan tanpa mempengaruhi stock

### Skenario 2: Store Manager Menyetujui
1. Status berubah menjadi `WAITING_OWNER_APPROVAL`
2. Stock batches **MASIH BELUM** diproses
3. Observer mendeteksi perubahan tapi tidak melakukan apa-apa

### Skenario 3: Owner Memberikan Approval Final
1. Status berubah menjadi `APPROVED`
2. Observer mendeteksi perubahan status
3. `ProductInStockService::processApprovedProductIn()` dipanggil
4. Stock batches dibuat dan stock movements dicatat
5. Notifikasi sukses dikirim

### Skenario 4: Penolakan di Tahap Manapun
1. Status berubah menjadi `REJECTED`
2. Observer mendeteksi perubahan status
3. Jika ada stock batches yang sudah diproses, akan dihapus
4. Notifikasi penolakan dikirim

### Skenario 5: Owner Langsung Membuat Produk Masuk
1. Status langsung `APPROVED` saat dibuat
2. `ProductInStockService::processNewProductIn()` dipanggil di `afterCreate()`
3. Stock batches langsung diproses

### Skenario 6: Edit Produk Masuk
1. Jika status masih pending: Stock batches tidak diproses
2. Jika status sudah approved: Stock batches di-reprocess dengan data baru
3. Menggunakan `ProductInStockService::reprocessStockBatches()`

## Keamanan dan Integritas Data

### Pengecekan Status
- Semua method stock processing memiliki pengecekan `isApproved()`
- Log dicatat untuk setiap operasi stock processing
- Transaksi database digunakan untuk memastikan konsistensi

### Rollback Mechanism
- Jika produk ditolak setelah diproses, stock batches akan dihapus
- Stock movements terkait juga akan dibersihkan
- Batch quantities akan dihitung ulang

### Logging
- Setiap operasi stock processing dicatat di log
- Informasi yang dicatat: product_in_id, status, jumlah items
- Memudahkan debugging dan audit trail

## Testing Scenarios

### Test Case 1: Normal Approval Flow
1. Admin packing buat produk masuk → Stock tidak berubah
2. Store manager approve → Stock masih tidak berubah  
3. Owner approve → Stock bertambah sesuai items

### Test Case 2: Rejection Flow
1. Admin packing buat produk masuk → Stock tidak berubah
2. Store manager reject → Stock tetap tidak berubah
3. Atau owner reject setelah manager approve → Stock tetap tidak berubah

### Test Case 3: Owner Direct Creation
1. Owner buat produk masuk → Status langsung approved
2. Stock langsung bertambah

### Test Case 4: Edit Approved Product
1. Edit produk yang sudah approved → Stock di-reprocess
2. Edit produk yang masih pending → Stock tidak diproses

## Catatan Implementasi

- Observer pattern digunakan untuk memisahkan concern approval dari stock processing
- Service pattern digunakan untuk mengorganisir logika stock processing
- Backward compatibility dijaga dengan tetap menggunakan service yang sudah ada
- Error handling dan logging ditambahkan untuk monitoring
- Transaksi database memastikan data consistency
