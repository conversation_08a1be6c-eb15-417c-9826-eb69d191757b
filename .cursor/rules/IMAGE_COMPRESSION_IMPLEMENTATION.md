# Image Compression Implementation

## Overview

Implementasi kompresi gambar menggunakan Intervention Image untuk foto bukti di sistem produk masuk dan produk keluar. Sistem ini secara otomatis mengkompresi gambar yang diupload untuk menghemat ruang penyimpanan sambil mempertahankan kualitas yang baik.

## Features

### 1. Automatic Image Compression

-   **Quality**: 80% (balance antara kualitas dan ukuran file)
-   **Format**: Semua gambar dikonversi ke JPEG untuk konsistensi
-   **Dynamic Sizing**: Tidak ada cropping paksa, user bebas upload aspect ratio apapun
-   **Max Dimensions**: 1920x1080 (dapat dikonfigurasi)

### 2. Smart Resizing

-   Gambar yang lebih besar dari max dimensions akan di-resize dengan mempertahankan aspect ratio
-   Gambar yang lebih kecil tidak akan di-upscale
-   Tidak ada cropping otomatis yang memotong bagian penting foto

### 3. Fallback Mechanism

-   Jika kompresi gagal, file original tetap tersimpan
-   Notifikasi peringatan ditampilkan jika ada kegagalan kompresi
-   Sistem tetap berfungsi normal meski ada error kompresi

## Configuration

### Environment Variables (.env)

```env
# Image Compression Settings
IMAGE_COMPRESSION_ENABLED=true
IMAGE_COMPRESSION_QUALITY=80
IMAGE_COMPRESSION_MAX_WIDTH=1920
IMAGE_COMPRESSION_MAX_HEIGHT=1080
IMAGE_COMPRESSION_SHOW_SUCCESS=false
IMAGE_COMPRESSION_SHOW_FAILURE=true
```

### Config File (config/image.php)

-   `compression.quality`: Level kualitas JPEG (1-100)
-   `compression.max_width`: Lebar maksimal gambar
-   `compression.max_height`: Tinggi maksimal gambar
-   `compression.enabled`: Enable/disable kompresi global
-   `compression.supported_formats`: Format yang didukung
-   `compression.notifications`: Setting notifikasi

## Implementation Details

### 1. ImageCompressionService

**Location**: `app/Services/ImageCompressionService.php`

**Methods**:

-   `compressImage()`: Kompresi single image
-   `compressMultipleImages()`: Kompresi multiple images
-   `calculateNewDimensions()`: Hitung dimensi baru dengan aspect ratio
-   `getSettings()`: Get compression settings

**Features**:

-   Preserve aspect ratio saat resize
-   Convert semua format ke JPEG
-   Logging untuk monitoring
-   Error handling yang robust

### 2. CompressedFileUpload Component

**Location**: `app/Filament/Components/CompressedFileUpload.php`

**Features**:

-   Static factory class untuk Filament FileUpload
-   Auto-compression setelah upload
-   Method `evidencePhotos()` untuk konfigurasi optimal
-   Support temporary file compression
-   Notification system

**Usage**:

```php
CompressedFileUpload::evidencePhotos('evidence_photos')
    ->directory('product-in-evidence')
```

### 3. Updated Resources

**Files Updated**:

-   `app/Filament/Resources/ProductInResource.php`
-   `app/Filament/Resources/ProductInResource/Pages/EditProductIn.php`
-   `app/Filament/Resources/ProductOutResource.php`
-   `app/Filament/Resources/ProductOutResource/Pages/EditProductOut.php`

**Changes**:

-   Replace `Forms\Components\FileUpload` dengan `CompressedFileUpload`
-   Remove manual resize settings (handled by compression service)
-   Add import untuk `CompressedFileUpload`

## Benefits

### 1. Storage Optimization

-   Significant reduction in file sizes
-   Consistent JPEG format
-   Optimal quality vs size balance

### 2. Performance Improvement

-   Faster page loading
-   Reduced bandwidth usage
-   Better user experience

### 3. Consistency

-   All images in same format (JPEG)
-   Standardized quality level
-   Predictable file sizes

### 4. Flexibility

-   Dynamic sizing without forced cropping
-   Configurable compression settings
-   Easy to enable/disable

## Monitoring & Logging

### Log Information

-   Original file size vs compressed size
-   Compression ratio percentage
-   Original vs new dimensions
-   Success/failure status

### Log Location

-   Laravel log files (`storage/logs/laravel.log`)
-   Search for "Image compressed successfully" or "Image compression failed"

### Example Log Entry

```
[2025-01-XX XX:XX:XX] local.INFO: Image compressed successfully {
    "original_file": "product-in-evidence/photo.png",
    "new_file": "product-in-evidence/photo.jpg",
    "original_size": 2048576,
    "new_size": 512000,
    "compression_ratio": "75.0%",
    "original_dimensions": "3000x2000",
    "new_dimensions": "1920x1280"
}
```

## Troubleshooting

### Common Issues

1. **Compression Fails**

    - Check if Intervention Image is properly installed
    - Verify GD extension is enabled
    - Check file permissions

2. **Memory Issues**

    - Increase PHP memory limit for large images
    - Consider reducing max dimensions

3. **Quality Issues**
    - Adjust `IMAGE_COMPRESSION_QUALITY` setting
    - Test with different quality levels

### Error Handling

-   All errors are logged with full stack trace
-   Fallback to original file if compression fails
-   User notifications for failed compressions
-   System continues to function normally

## Future Enhancements

### Possible Improvements

1. **WebP Support**: Convert to WebP for even smaller files
2. **Progressive JPEG**: Better loading experience
3. **Batch Processing**: Command to compress existing images
4. **Advanced Settings**: Per-directory compression settings
5. **Image Optimization**: Additional optimization techniques

### Configuration Options

1. **Quality Profiles**: Different quality settings for different use cases
2. **Format Selection**: Choose output format per upload
3. **Conditional Compression**: Compress only if file size > threshold
4. **Backup Options**: Keep original files as backup

## Dependencies

### Required Packages

-   `intervention/image: ^3.0` - Image processing library
-   `intervention/gif: ^4.2` - GIF support (auto-installed)

### PHP Extensions

-   GD extension (for image processing)
-   Fileinfo extension (for file type detection)

### Laravel Version

-   Compatible with Laravel 12.x
-   Filament 3.x support
