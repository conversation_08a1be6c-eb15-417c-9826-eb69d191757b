---
description: Dokumentasi penambahan kolom baru pada tabel list produk stok untuk role owner dan kepala toko
globs:
alwaysApply: false
---

# Stock Table Enhancements

## Overview

Menambahkan kolom-kolom baru pada tabel list produk stok (`StockBatchResource`) yang hanya ditampilkan untuk role owner dan store_manager (kepala toko).

## Kolom Baru yang Ditambahkan

### 1. <PERSON><PERSON>

-   **Label**: "Nilai Stok"
-   **Deskripsi**: Menghitung total nilai stok berdasarkan harga beli × total stok (normal + bad)
-   **Format**: Currency (IDR)
-   **Visibility**: Hanya untuk owner dan store_manager (`canViewPricing()`)
-   **Logic**:
    -   Untuk produk paket: return 0 (tidak memiliki nilai stok langsung)
    -   Untuk produk satuan: sum dari (quantity + bad_quantity) × purchase_price untuk setiap batch

### 2. Stok Expired < 1 Tahun

-   **Label**: "Expired < 1 Tahun"
-   **Deskripsi**: Total stok yang akan expired dalam 1 tahun ke depan
-   **Format**: Numeric badge (warning jika > 0, gray jika 0)
-   **Visibility**: Hanya untuk owner dan store_manager
-   **Logic**:
    -   Untuk produk paket: return 0
    -   Untuk produk satuan: sum stok dari batch yang expiration_date <= 1 tahun dari sekarang

### 3. Stok Expired < 6 Bulan

-   **Label**: "Expired < 6 Bulan"
-   **Deskripsi**: Total stok yang akan expired dalam 6 bulan ke depan
-   **Format**: Numeric badge (danger jika > 0, gray jika 0)
-   **Visibility**: Hanya untuk owner dan store_manager
-   **Logic**:
    -   Untuk produk paket: return 0
    -   Untuk produk satuan: sum stok dari batch yang expiration_date <= 6 bulan dari sekarang

### 4. Stok Expired

-   **Label**: "Expired"
-   **Deskripsi**: Total stok yang sudah expired (lewat tanggal kedaluwarsa)
-   **Format**: Numeric badge (danger jika > 0, gray jika 0)
-   **Visibility**: Hanya untuk owner dan store_manager
-   **Logic**:
    -   Untuk produk paket: return 0
    -   Untuk produk satuan: sum stok dari batch yang expiration_date < sekarang

## Technical Implementation

### File yang Dimodifikasi

-   `app/Filament/Resources/StockBatchResource.php`
-   `app/Http/Controllers/StockPdfController.php`
-   `resources/views/pdf/total-stock.blade.php`

### Dependencies yang Ditambahkan

-   `Carbon\Carbon` untuk manipulasi tanggal

### Query Optimization

-   Menambahkan `stockBatches` relationship ke dalam `with()` untuk menghindari N+1 query problem
-   Menggunakan eager loading untuk mengakses data batch secara efisien

### Role-based Visibility

-   Menggunakan `$canViewPricing = $user?->canViewPricing() ?? false`
-   Kolom hanya ditampilkan jika user memiliki permission `canViewPricing()` (owner dan store_manager)

## Business Logic

### Produk Paket vs Produk Satuan

-   **Produk Paket**: Kolom baru menampilkan 0 atau tidak relevan karena paket tidak memiliki stok batch langsung
-   **Produk Satuan**: Kolom menghitung berdasarkan data stock_batches yang terkait

### Perhitungan Stok

-   Menggunakan `quantity + bad_quantity` untuk total stok (tidak termasuk unusable_quantity)
-   Sesuai dengan business rule bahwa unusable stock tidak dihitung dalam total stok

### Color Coding

-   **Warning (Orange)**: Untuk stok yang akan expired dalam 1 tahun
-   **Danger (Red)**: Untuk stok yang akan expired dalam 6 bulan atau sudah expired
-   **Gray**: Untuk nilai 0 atau tidak ada stok yang expired

## Usage Notes

1. Kolom-kolom ini hanya terlihat oleh user dengan role `owner` atau `store_manager`
2. Admin packing tidak dapat melihat kolom-kolom ini karena tidak memiliki akses pricing
3. Semua kolom dapat di-sort untuk memudahkan analisis
4. Nilai stok ditampilkan dalam format currency IDR
5. Kolom expired menggunakan badge untuk visual indicator yang jelas

## PDF Export Enhancement

### PDF Total Stock Report

Kolom-kolom baru juga ditambahkan ke dalam PDF download total stok:

#### Perubahan pada StockPdfController:

-   Menambahkan eager loading `stockBatches` relationship
-   Menambahkan perhitungan nilai stok dan expired stock untuk setiap produk
-   Menggunakan `map()` function untuk menghitung data tambahan

#### Perubahan pada PDF Template:

-   Menambahkan 4 kolom baru: Exp < 1Thn, Exp < 6Bln, Expired, Nilai Stok
-   **Nilai Stok dipindahkan ke kolom paling kanan** untuk emphasis
-   Menyesuaikan width kolom untuk mengakomodasi kolom tambahan
-   Mengurangi font size untuk memuat lebih banyak kolom
-   Produk paket menampilkan '-' untuk kolom yang tidak relevan

#### Layout Optimization:

-   Font size dikurangi dari 12px ke 10px untuk body
-   Table cell font size dikurangi ke 9px
-   Padding dikurangi dari 8px ke 4px
-   Width kolom disesuaikan untuk total 100%

#### Total Summary Row:

-   Menambahkan baris "TOTAL" di bagian bawah tabel
-   Menampilkan total keseluruhan untuk semua kolom numerik
-   **Total Nilai Stok** ditampilkan dengan format currency
-   Background color khusus untuk membedakan dari data produk
-   Border atas yang lebih tebal untuk pemisahan visual
-   Hanya ditampilkan jika ada data produk
