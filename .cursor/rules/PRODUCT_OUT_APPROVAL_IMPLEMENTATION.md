# Implementasi Sistem Approval Produk Keluar

## Overview

Implementasi sistem approval bertingkat untuk produk keluar yang sama persis dengan sistem approval produk masuk. Sistem ini memastikan bahwa stock hanya dikurangi setelah mendapat persetujuan penuh dari store manager dan owner.

## Komponen yang Diimplementasikan

### 1. ProductOutStatus Enum

**File:** `app/Models/ProductOutStatus.php`

Enum untuk status approval dengan nilai:

-   `WAITING_STORE_MANAGER_APPROVAL`: Menunggu persetujuan kepala toko
-   `WAITING_OWNER_APPROVAL`: Menunggu persetujuan owner
-   `APPROVED`: Disetujui
-   `REJECTED`: Ditolak

### 2. Database Migration

**File:** `database/migrations/2025_06_07_020000_add_approval_columns_to_product_outs_table.php`

Menambahkan kolom approval ke tabel `product_outs`:

-   `status` (enum)
-   `manager_approved_by`, `owner_approved_by` (foreign keys)
-   `manager_approved_at`, `owner_approved_at` (timestamps)
-   `rejection_reason` (text)
-   `evidence_photos` (json)

**File:** `database/migrations/2025_06_07_030000_add_approval_support_to_product_out_items_table.php`

Mengubah `batch_id` menjadi nullable untuk mendukung approval system.

**File:** `database/migrations/2025_06_07_040000_add_product_id_to_product_out_items_table.php`

Menambahkan kolom `product_id` untuk referensi langsung ke produk.

### 3. Model ProductOut

**File:** `app/Models/ProductOut.php`

Ditambahkan:

-   Fillable fields untuk approval
-   Casts untuk status dan timestamps
-   Relasi ke manager dan owner approver
-   Helper methods: `canBeApprovedByStoreManager()`, `canBeApprovedByOwner()`, `isApproved()`, `isRejected()`, `isPending()`
-   Method untuk edit support: `getFormattedItemsForEdit()`, `calculatePackageSalePrice()`, `calculatePackageFinalPrice()`

### 4. ProductOutObserver

**File:** `app/Observers/ProductOutObserver.php`

Observer untuk menangani perubahan status:

-   **Status berubah ke APPROVED**: Memproses stock secara otomatis
-   **Status berubah ke REJECTED**: Mengembalikan stock jika sudah diproses
-   **Model dihapus**: Mengembalikan stock sebelum penghapusan

### 5. ProductOutStockService

**File:** `app/Services/ProductOutStockService.php`

Service untuk menangani stock processing:

-   `processApprovedProductOut()`: Memproses stock untuk produk yang sudah approved
-   `processNewProductOut()`: Memproses produk baru (hanya jika langsung approved)
-   `restoreStockForRejectedProductOut()`: Mengembalikan stock untuk produk yang ditolak
-   `hasProcessedStock()`: Mengecek apakah produk sudah memiliki stock movements
-   `reprocessStockForEditedProductOut()`: Reprocess stock untuk produk yang diedit (hanya jika sudah approved)

### 6. Resource dan Pages

#### ProductOutResource

**File:** `app/Filament/Resources/ProductOutResource.php`

Ditambahkan:

-   Navigation badge untuk pending approvals
-   `canEdit()` method untuk mengatur hak edit
-   Form upload foto bukti
-   Infolist dengan informasi approval dan foto
-   Kolom status di tabel
-   Filter status

#### CreateProductOut

**File:** `app/Filament/Resources/ProductOutResource/Pages/CreateProductOut.php`

Dimodifikasi:

-   Set status default berdasarkan role user
-   Simpan items tanpa memproses stock
-   Hanya proses stock jika langsung approved (owner)

#### ViewProductOut

**File:** `app/Filament/Resources/ProductOutResource/Pages/ViewProductOut.php`

Ditambahkan tombol approve/reject untuk manager dan owner.

#### EditProductOut

**File:** `app/Filament/Resources/ProductOutResource/Pages/EditProductOut.php`

Dimodifikasi dengan fitur edit lengkap:

-   **Hak edit berdasarkan role dan status**
-   **Form edit lengkap** dengan semua field yang bisa diedit
-   **Preservasi status approval** untuk kepala toko dan owner
-   **Reprocess items** setelah edit
-   **Reprocess stock** untuk produk yang sudah approved
-   **Format data items** untuk edit form (konversi paket kembali ke format paket)

#### ApprovalProductOut

**File:** `app/Filament/Resources/ProductOutResource/Pages/ApprovalProductOut.php`

Page khusus untuk approval dengan tombol approve/reject.

#### ListProductOuts

**File:** `app/Filament/Resources/ProductOutResource/Pages/ListProductOuts.php`

Ditambahkan tabs filter berdasarkan status dan notification badges.

## Alur Kerja Sistem

### Skenario 1: Admin Packing Membuat Produk Keluar

1. Admin packing membuat record dengan status `WAITING_STORE_MANAGER_APPROVAL`
2. Items tersimpan dengan `batch_id = null`
3. Stock **TIDAK** dikurangi
4. Record tersimpan tanpa mempengaruhi stock

### Skenario 2: Store Manager Menyetujui

1. Status berubah menjadi `WAITING_OWNER_APPROVAL`
2. Stock **MASIH BELUM** dikurangi
3. Observer mendeteksi perubahan tapi tidak melakukan apa-apa

### Skenario 3: Owner Memberikan Approval Final

1. Status berubah menjadi `APPROVED`
2. Observer mendeteksi perubahan status
3. `ProductOutStockService::processApprovedProductOut()` dipanggil
4. Stock dikurangi dan stock movements dicatat
5. `batch_id` diisi pada items
6. Notifikasi sukses dikirim

### Skenario 4: Penolakan

1. Status berubah menjadi `REJECTED`
2. Jika stock sudah diproses, akan dikembalikan
3. Stock movements pengembalian dicatat

### Skenario 5: Owner Langsung Membuat

1. Status langsung `APPROVED`
2. Stock langsung diproses saat create
3. Tidak perlu approval tambahan

## Fitur Utama

### 1. Upload Foto Bukti

-   Multiple photos (maksimal 5)
-   Resize otomatis ke 1920x1080
-   Crop ratio 16:9
-   Disimpan di `storage/app/public/product-out-evidence/`

### 2. Notification Badges

-   Badge di navigation menu untuk pending approvals
-   Badge di tabs untuk jumlah record per status

### 3. Tabs Filter

-   **Semua**: Untuk user dengan full access
-   **Menunggu Persetujuan Kepala Toko**: Untuk store manager dan owner
-   **Menunggu Persetujuan Owner**: Untuk owner
-   **Disetujui**: Untuk user dengan full access
-   **Ditolak**: Untuk user dengan full access

### 4. Hak Edit (Sama seperti Produk Masuk)

-   **Admin packing**: Hanya bisa edit ketika status belum berubah ke menunggu persetujuan owner (hanya saat `waiting_store_manager_approval`)
-   **Kepala toko/owner**: Bisa mengedit sebelum melakukan approval dan status tidak berubah setelah di edit
-   **Form edit lengkap**: Mendukung edit semua field termasuk items, foto bukti, dan informasi transaksi
-   **Reprocess stock**: Otomatis reprocess stock jika produk sudah approved dan ada perubahan items

### 5. Integrasi dengan Sistem Stock

-   Stock hanya dikurangi setelah approval penuh
-   Mendukung normal stock, bad stock, dan package products
-   Automatic stock restoration untuk produk yang ditolak

## Status Implementasi

✅ **SELESAI** - Sistem approval produk keluar sudah diimplementasikan lengkap dengan:

-   Enum status dan database schema
-   Model dengan helper methods dan edit support
-   Observer untuk stock processing
-   Service untuk stock management dan reprocessing
-   Resource dengan form upload foto
-   Pages dengan tombol approve/reject
-   Tabs dan notification badges
-   **Fitur edit lengkap sama seperti produk masuk**:
    -   Admin packing hanya bisa edit ketika status belum berubah ke menunggu persetujuan owner
    -   Kepala toko/owner bisa mengedit sebelum melakukan approval dan status tidak berubah setelah di edit
    -   Form edit lengkap dengan reprocess items dan stock
-   Integrasi dengan sistem stock existing

Sistem ini sekarang **100% konsisten** dengan sistem approval produk masuk dan siap digunakan.
