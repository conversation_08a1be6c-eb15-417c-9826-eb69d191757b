# Implementasi Package Tracking di Relation Manager

## Deskripsi
Implementasi sistem tracking paket untuk menampilkan informasi paket original beserta breakdown komponennya di relation manager, sehingga user dapat melihat:
1. Informasi paket original (nama paket + jumlah paket)
2. Detail breakdown komponen dari paket tersebut
3. Pengelompokan komponen berdasarkan paket yang sama

## Masalah yang Diselesaikan

**Sebelum implementasi:**
- Relation manager hanya menampilkan komponen individual hasil breakdown
- Informasi paket original hilang setelah breakdown
- Tidak ada cara untuk mengetahui komponen mana yang berasal dari paket yang sama
- Sulit melacak transaksi paket original

**Setelah implementasi:**
- Relation manager menampilkan informasi paket original
- Komponen dapat dikelompokkan berdasarkan paket asal
- Tracking lengkap dari paket ke komponen
- Filter dan pencarian berdasarkan paket

## Perubahan yang Dilakukan

### 1. Database Schema
**File:** `database/migrations/2025_01_15_000001_add_package_tracking_to_product_in_items.php`
**File:** `database/migrations/2025_01_15_000002_add_package_tracking_to_product_out_items.php`

**Kolom baru yang ditambahkan:**
- `package_product_id` - ID produk paket original
- `package_quantity` - Jumlah paket original yang di-breakdown
- `package_group_id` - UUID untuk mengelompokkan komponen dari paket yang sama

### 2. Model Updates
**File:** `app/Models/ProductInItem.php`
**File:** `app/Models/ProductOutItem.php`

**Fitur baru:**
- Relasi `packageProduct()` ke produk paket original
- Method `isFromPackage()` untuk cek apakah item berasal dari paket
- Attribute `getDisplayNameAttribute()` untuk nama display yang informatif

### 3. Service Updates
**File:** `app/Services/PackageStockService.php`

**Perubahan:**
- Generate UUID untuk `package_group_id` setiap breakdown paket
- Simpan tracking info saat membuat ProductInItem/ProductOutItem
- Update signature method `deductNormalStock()` dan `deductBadStock()`

### 4. Relation Manager Updates
**File:** `app/Filament/Resources/ProductInResource/RelationManagers/ItemsRelationManager.php`
**File:** `app/Filament/Resources/ProductOutResource/RelationManagers/ItemsRelationManager.php`

**Fitur baru:**
- Kolom "Informasi Produk" dengan icon 📦 untuk paket dan 📋 untuk produk satuan
- Kolom "Detail Komponen" untuk menampilkan breakdown
- Filter berdasarkan paket, hanya paket, atau hanya produk satuan
- Grouping berdasarkan `package_group_id`
- Kolom "Grup Paket" (tersembunyi default) untuk debugging

### 5. Create Process Updates
**File:** `app/Filament/Resources/ProductInResource/Pages/CreateProductIn.php`

**Perubahan:**
- Hapus item paket original setelah breakdown untuk menghindari duplikasi
- Proses breakdown dengan tracking info

## Cara Kerja

### Contoh Skenario
**Input:** 2 unit "Paket Combo A" (berisi 2 Produk B + 3 Produk C per paket)

**Proses:**
1. User input "2 unit Paket Combo A"
2. Sistem generate UUID: `abc123-def456-...`
3. Breakdown menjadi:
   - 4 unit Produk B (package_product_id=Paket Combo A, package_quantity=2, package_group_id=abc123...)
   - 6 unit Produk C (package_product_id=Paket Combo A, package_quantity=2, package_group_id=abc123...)
4. Item paket original dihapus dari database

**Tampilan di Relation Manager:**
```
📦 Paket Combo A (2 unit)
├─ └─ Produk B (4 unit)
└─ └─ Produk C (6 unit)

📋 Produk Satuan Lainnya
├─ Produk D (10 unit)
└─ Produk E (5 unit)
```

## Fitur Relation Manager

### 1. Kolom Informasi
- **Informasi Produk**: Menampilkan nama paket dengan icon 📦 atau produk satuan dengan icon 📋
- **Detail Komponen**: Menampilkan breakdown komponen dengan format tree `└─`
- **Brand, Quantity, Date, Price**: Kolom standar yang sudah ada
- **Grup Paket**: Kolom tersembunyi untuk debugging (menampilkan 8 karakter pertama UUID)

### 2. Filter
- **Filter Paket**: Dropdown untuk memilih paket tertentu
- **Hanya Paket**: Tampilkan hanya item yang berasal dari breakdown paket
- **Hanya Produk Satuan**: Tampilkan hanya produk satuan (bukan dari paket)

### 3. Grouping
- **Grup Paket**: Mengelompokkan komponen berdasarkan paket asal
- Collapsible untuk menghemat ruang
- Title menampilkan nama paket dan jumlah unit

### 4. Search
- Dapat mencari berdasarkan nama produk komponen
- Dapat mencari berdasarkan nama paket original

## Keuntungan

1. **Transparansi**: User dapat melihat paket original dan breakdownnya
2. **Traceability**: Mudah melacak komponen yang berasal dari paket tertentu
3. **Organization**: Pengelompokan yang rapi berdasarkan paket
4. **Filtering**: Filter fleksibel untuk berbagai kebutuhan analisis
5. **Backward Compatibility**: Produk satuan tetap ditampilkan normal
6. **Performance**: Menggunakan UUID grouping yang efisien

## Catatan Implementasi

1. **UUID Generation**: Setiap breakdown paket mendapat UUID unik untuk grouping
2. **Data Cleanup**: Item paket original dihapus setelah breakdown untuk menghindari duplikasi
3. **Relasi Efisien**: Menggunakan foreign key ke products table untuk performa optimal
4. **UI Consistency**: Icon dan format yang konsisten untuk membedakan paket vs produk satuan
5. **Extensible**: Struktur dapat diperluas untuk fitur tracking lainnya

## Testing

Untuk menguji implementasi:
1. Buat transaksi produk masuk dengan paket
2. Cek relation manager - harus menampilkan grouping paket
3. Test filter "Hanya Paket" dan "Hanya Produk Satuan"
4. Test pencarian berdasarkan nama paket dan komponen
5. Verifikasi data tracking di database

## File yang Terpengaruh

1. `database/migrations/2025_01_15_000001_add_package_tracking_to_product_in_items.php`
2. `database/migrations/2025_01_15_000002_add_package_tracking_to_product_out_items.php`
3. `app/Models/ProductInItem.php`
4. `app/Models/ProductOutItem.php`
5. `app/Services/PackageStockService.php`
6. `app/Filament/Resources/ProductInResource/RelationManagers/ItemsRelationManager.php`
7. `app/Filament/Resources/ProductOutResource/RelationManagers/ItemsRelationManager.php`
8. `app/Filament/Resources/ProductInResource/Pages/CreateProductIn.php`
