# Dokumentasi Proses Bisnis - Product Out (Penjualan/Distribusi)

## 1. Tujuan Bisnis

Product Out mengelola proses penjualan dan distribusi produk dengan sistem approval bertingkat dan reservasi stok. Sistem ini mengimplementasikan logika FEFO (First Expired First Out) dan <PERSON> (Lowest Cost First) untuk optimalisasi inventory.

## 2. Role dan Permission

### 2.1 Akses Product Out
- **Owner**: <PERSON><PERSON><PERSON> penuh, dapat membuat, edit, approve, dan melihat semua record
- **Store Manager**: <PERSON><PERSON><PERSON> penuh, dapat membuat, edit, approve, dan melihat semua record  
- **Packing Admin**: A<PERSON><PERSON> terbatas, hanya dapat membuat dan edit record sendiri

### 2.2 Pembatasan Berdasarkan Role
**Packing Admin**:
- Hanya melihat record yang dibuat sendiri
- Tidak dapat approve record
- Tidak dapat melihat informasi pricing

**Store Manager & Owner**:
- Dapat melihat semua record
- Dapat melakukan approval
- Dapat melihat informasi pricing dan profit

## 3. Sistem Approval Bertingkat

### 3.1 Alur Approval
```
Packing Admin → Store Manager → Owner
     ↓              ↓           ↓
   Create      Approve 1st   Approve Final
   Reserve     Reserve       Deduct Stock
```

### 3.2 Status Approval
- **WAITING_STORE_MANAGER_APPROVAL**: Menunggu persetujuan kepala toko
- **WAITING_OWNER_APPROVAL**: Menunggu persetujuan owner
- **APPROVED**: Disetujui lengkap, stok dikurangi
- **REJECTED**: Ditolak, reservasi dibatalkan

### 3.3 Stock Processing Logic
- **Saat Create**: Stok direservasi menggunakan FEFO+LCOF
- **Saat Approval**: Reservasi dipertahankan
- **Saat Final Approval**: Stok benar-benar dikurangi
- **Saat Rejection**: Reservasi dibatalkan, stok dikembalikan

## 4. Workflow Product Out

### 4.1 Pembuatan Product Out
1. **Akses Form Create**:
   - Masuk ke menu Product Out
   - Klik "Create Product Out"

2. **Input Data Header**:
   - Tanggal keluar
   - Pilih sales channel
   - Pilih stock type (good/bad)
   - Input catatan (opsional)
   - Upload foto bukti (multiple files)

3. **Input Items**:
   - Pilih produk dari dropdown (dengan prefix notation)
   - Input quantity
   - Sistem otomatis cek ketersediaan stok
   - **Error jika stok tidak mencukupi**

4. **Stock Reservation**:
   - Sistem otomatis reservasi stok menggunakan FEFO+LCOF
   - Untuk produk paket: reservasi komponen sesuai rasio
   - Stok reserved tidak tersedia untuk transaksi lain

5. **Submit**:
   - Status sesuai role pembuat
   - Redirect ke halaman view

### 4.2 FEFO + LCOF Logic

#### 4.2.1 Prioritas Reservasi
1. **Tanggal kadaluarsa** (ascending - yang paling dekat dulu)
2. **Harga pembelian** (ascending - yang termurah dulu)
3. **Created timestamp** (ascending - yang lama dulu)

#### 4.2.2 Implementasi untuk Stok Normal
```
SELECT * FROM stock_batches 
WHERE product_id = ? AND quantity > 0
ORDER BY expiration_date ASC, purchase_price ASC, created_at ASC
```

#### 4.2.3 Implementasi untuk Stok Buruk
```
SELECT * FROM stock_batches 
WHERE product_id = ? AND bad_quantity > 0
ORDER BY expiration_date ASC, purchase_price ASC, created_at ASC
```

### 4.3 Approval oleh Store Manager
1. **Review Reservasi**:
   - Cek detail produk yang direservasi
   - Verifikasi foto bukti
   - Review batch yang digunakan

2. **Approval Action**:
   - Klik "Setujui (Kepala Toko)"
   - Status berubah: WAITING_OWNER_APPROVAL
   - Reservasi dipertahankan

3. **Rejection**:
   - Klik "Tolak" dengan alasan
   - Status berubah: REJECTED
   - **Reservasi dibatalkan otomatis**

### 4.4 Final Approval oleh Owner
1. **Final Review**:
   - Cek semua detail dan approval sebelumnya
   - Verifikasi profit margin

2. **Final Approval**:
   - Klik "Setujui (Owner)"
   - Status berubah: APPROVED
   - **Stok benar-benar dikurangi**
   - **Stock movements dicatat**

3. **Final Rejection**:
   - Status berubah: REJECTED
   - **Reservasi dibatalkan otomatis**

## 5. Handling Produk Paket

### 5.1 Reservasi Komponen
- **Breakdown otomatis**: Paket dipecah ke komponen
- **Quantity calculation**: Sesuai rasio komponen
- **FEFO per komponen**: Setiap komponen mengikuti FEFO sendiri

### 5.2 Price Calculation
- **Sale price**: Dibagi rata dari harga paket
- **Final price**: Setelah diskon (jika ada)
- **Profit calculation**: Final price - purchase price per komponen

### 5.3 Stock Validation
- **Minimum component check**: Semua komponen harus tersedia
- **Package stock calculation**: MIN(komponen_stock / komponen_ratio)

## 6. Sistem Diskon

### 6.1 Jenis Diskon
- **Percentage discount**: Diskon persentase
- **Fixed amount discount**: Diskon nominal tetap
- **Manual discount**: Input manual oleh user

### 6.2 Aplikasi Diskon
- **Automatic**: Berdasarkan aturan diskon aktif
- **Manual override**: User dapat input diskon manual
- **Validation**: Diskon tidak boleh melebihi sale price

### 6.3 Calculation Logic
```
Final Price = Sale Price - Discount Amount
Profit = Final Price - Purchase Price
```

## 7. Business Rules

### 7.1 Stock Validation
- **Insufficient stock prevention**: Tidak boleh create jika stok tidak cukup
- **Real-time stock check**: Validasi saat input quantity
- **Reserved stock exclusion**: Stok yang direservasi tidak tersedia

### 7.2 FEFO Implementation
- **Strict FEFO**: Harus menggunakan batch dengan kadaluarsa terdekat
- **LCOF secondary**: Jika tanggal sama, gunakan harga termurah
- **No manual batch selection**: Sistem otomatis pilih batch

### 7.3 Package Rules
- **Component availability**: Semua komponen harus tersedia
- **Atomic operation**: Semua komponen berhasil atau semua gagal
- **Consistent pricing**: Harga komponen konsisten dengan paket

## 8. Fitur Khusus

### 8.1 Stock Type Selection
- **Good stock**: Stok normal dengan kondisi baik
- **Bad stock**: Stok buruk dengan harga khusus
- **Separate FEFO**: Masing-masing punya logika FEFO sendiri

### 8.2 Real-time Stock Check
- **Live validation**: Cek stok saat input quantity
- **Error prevention**: Mencegah overselling
- **User feedback**: Pesan error yang informatif

### 8.3 Batch Information Display
- **Relation manager**: Menampilkan batch yang digunakan
- **Expiration info**: Tanggal kadaluarsa per batch
- **Price breakdown**: Harga pembelian per batch

## 9. Integrasi dengan Modul Lain

### 9.1 Stock Management
- **Stock reservation**: Menggunakan FEFO+LCOF logic
- **Stock deduction**: Setelah approval final
- **Movement recording**: Mencatat perpindahan stok

### 9.2 Sales Channel
- **Channel tracking**: Mencatat saluran penjualan
- **Performance metrics**: Data untuk analisis channel

### 9.3 Discount Management
- **Automatic discount**: Aplikasi diskon otomatis
- **Discount validation**: Validasi aturan diskon

### 9.4 Returns Management
- **Return reference**: Basis untuk proses return
- **Batch tracking**: Tracking batch untuk return

## 10. Validasi dan Kontrol

### 10.1 Stock Validation
- **Availability check**: Validasi ketersediaan stok
- **Reservation integrity**: Memastikan reservasi konsisten
- **FEFO compliance**: Memastikan FEFO diikuti

### 10.2 Business Logic Validation
- **Price validation**: Validasi harga dan diskon
- **Profit calculation**: Perhitungan profit yang akurat
- **Package consistency**: Validasi konsistensi paket

### 10.3 Access Control
- **Role-based access**: Sesuai dengan permission user
- **Record ownership**: Packing admin hanya akses record sendiri
- **Approval authority**: Hanya authorized user yang bisa approve

## 11. Error Handling

### 11.1 Stock Errors
- **Insufficient stock**: Pesan error jelas dengan saran
- **Reservation conflicts**: Handling konflik reservasi
- **FEFO violations**: Mencegah pelanggaran FEFO

### 11.2 System Errors
- **Database errors**: Graceful error handling
- **Calculation errors**: Validasi perhitungan
- **Integration errors**: Handling error integrasi

## 12. Outcome dan Manfaat

### 12.1 Untuk Operasional
- **Inventory optimization**: FEFO mengurangi waste
- **Accurate stock**: Reservasi mencegah overselling
- **Efficient workflow**: Proses yang terstruktur

### 12.2 Untuk Manajemen
- **Profit visibility**: Tracking profit per transaksi
- **Channel performance**: Analisis performa channel
- **Control mechanism**: Approval untuk kontrol

### 12.3 Untuk Bisnis
- **Cost optimization**: LCOF mengurangi COGS
- **Quality control**: FEFO memastikan produk fresh
- **Revenue tracking**: Tracking penjualan akurat
