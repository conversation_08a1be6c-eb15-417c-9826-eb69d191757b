# Dokumentasi Proses Bisnis - Dashboard

## 1. <PERSON>juan <PERSON>nis

Dashboard berfungsi sebagai pusat informasi utama yang memberikan gambaran menyeluruh tentang kondisi bisnis dan operasional gudang secara real-time. Dashboard membantu manajemen dalam pengambilan keputusan strategis dan operasional.

## 2. Role dan Permission

### 2.1 Akses Dashboard

-   **Owner**: <PERSON><PERSON><PERSON> penuh ke semua widget dan filter
-   **Store Manager**: <PERSON><PERSON><PERSON> penuh ke semua widget dan filter
-   **Packing Admin**: <PERSON><PERSON><PERSON> terbatas, hanya melihat widget stok hampir habis dan akan kedaluwarsa

### 2.2 Pembatasan Data

-   **Packing Admin**: Hanya melihat widget operasional (stok hampir habis, akan kedaluwarsa) tanpa filter
-   **Store Manager & Owner**: Melihat semua widget termasuk finansial dengan filter lengkap

## 3. Komponen Dashboard

### 3.1 Filter Dashboard

**Lokasi**: Bagian atas dashboard dalam card terpisah

**Filter yang Tersedia** (hanya untuk Owner dan Store Manager):

-   **<PERSON><PERSON>lai**: Untuk menentukan periode awal analisis
-   **Tanggal Akhir**: Untuk menentukan periode akhir analisis
-   **Saluran Penjualan**: Filter berdasarkan channel (TikTok, Shopee, Offline Store, dll)

**Layout**: 3 kolom dalam satu card untuk organisasi visual yang lebih baik

**Default Values**:

-   Tanggal Mulai: Awal bulan berjalan
-   Tanggal Akhir: Akhir bulan berjalan
-   Saluran Penjualan: Semua saluran

**Akses**: Tidak ditampilkan untuk Packing Admin karena tidak relevan dengan widget operasional

### 3.2 Widget Statistik Penjualan (SalesStatsWidget)

**Tujuan**: Menampilkan metrik keuangan utama

**Metrik yang Ditampilkan**:

-   **Revenue (Pendapatan Kotor)**: Total penjualan sebelum dikurangi biaya
-   **Profit (Keuntungan)**: Revenue dikurangi harga pembelian produk
-   **Total Order**: Jumlah transaksi penjualan

**Responsif terhadap Filter**:

-   Periode tanggal yang dipilih
-   Saluran penjualan yang dipilih

**Akses**: Hanya Store Manager dan Owner (tidak ditampilkan untuk Packing Admin)

### 3.3 Widget Grafik Penjualan (SalesChartWidget)

**Tujuan**: Visualisasi tren penjualan dalam bentuk grafik

**Fitur**:

-   **Multiple Time Periods**: Daily, Weekly, Monthly, Yearly
-   **Interactive Chart**: Dapat di-zoom dan di-pan
-   **Responsive Design**: Menyesuaikan dengan ukuran layar

**Data yang Ditampilkan**:

-   Tren penjualan berdasarkan periode yang dipilih
-   Breakdown per saluran penjualan (jika filter saluran tidak dipilih)

**Akses**: Hanya Store Manager dan Owner (tidak ditampilkan untuk Packing Admin)

### 3.4 Widget Stok Rendah (LowStockWidget)

**Tujuan**: Monitoring produk dengan stok di bawah batas minimum

**Fitur**:

-   **Pagination**: Untuk manajemen data yang lebih baik
-   **Maximum Height**: Dengan scroll functionality
-   **Collapsible**: Dapat diciutkan untuk menghemat ruang

**Kriteria Stok Rendah**:

-   Produk dengan total stok < 10 unit
-   Termasuk stok normal dan stok buruk
-   Tidak termasuk produk paket (karena stok dihitung dari komponen)

**Layout**: 2 kolom (bukan 3 kolom)

**Akses**: Semua role (termasuk Packing Admin)

### 3.5 Widget Mendekati Kadaluarsa (NearExpiryWidget)

**Tujuan**: Monitoring produk yang mendekati tanggal kadaluarsa

**Fitur**:

-   **Pagination**: Untuk manajemen data yang lebih baik
-   **Maximum Height**: Dengan scroll functionality
-   **Collapsible**: Dapat diciutkan untuk menghemat ruang

**Filter Kadaluarsa**:

-   **30 Hari**: Produk yang kadaluarsa dalam 30 hari (default)
-   **< 6 bulan**: Produk yang kadaluarsa dalam 6 bulan
-   **< 1 tahun**: Produk yang kadaluarsa dalam 1 tahun

**Catatan**: Filter tidak memiliki opsi "semua" dan selalu menampilkan data berdasarkan periode yang dipilih

**Kriteria**:

-   Berdasarkan tanggal kadaluarsa batch stok
-   Hanya menampilkan batch dengan stok > 0
-   Termasuk stok normal dan stok buruk

**Layout**: 2 kolom (bukan 3 kolom)

**Akses**: Semua role (termasuk Packing Admin)

## 4. Workflow Dashboard

### 4.1 Akses Dashboard

1. User login ke sistem
2. Sistem mengarahkan ke halaman dashboard
3. Dashboard menampilkan greeting dengan nama user dan tanggal
4. Widget dimuat sesuai dengan role user

### 4.2 Penggunaan Filter (Owner dan Store Manager)

1. User mengakses filter di bagian atas dashboard
2. User memilih periode tanggal yang diinginkan
3. User memilih saluran penjualan (opsional)
4. Sistem memperbarui widget yang responsif terhadap filter
5. Data ditampilkan sesuai dengan filter yang dipilih

**Catatan**: Packing Admin tidak melihat filter karena hanya menggunakan widget operasional

### 4.3 Monitoring Stok

1. User melihat widget stok rendah dan mendekati kadaluarsa
2. Jika ada produk yang memerlukan perhatian, user dapat:
    - Klik pada produk untuk melihat detail
    - Melakukan tindakan seperti pemesanan ulang
    - Melakukan promosi untuk produk mendekati kadaluarsa

### 4.4 Analisis Penjualan

1. Store Manager/Owner melihat widget statistik penjualan
2. Menganalisis tren melalui grafik penjualan
3. Menggunakan filter untuk analisis periode tertentu
4. Membuat keputusan bisnis berdasarkan data yang ditampilkan

## 5. Business Rules

### 5.1 Perhitungan Finansial

-   **Revenue**: Sum dari (quantity × final_price) semua item terjual
-   **Profit**: Revenue - Sum dari (quantity × purchase_price) semua item terjual
-   **Hanya transaksi dengan status APPROVED** yang dihitung

### 5.2 Perhitungan Stok

-   **Stok Normal**: Quantity dari batch dengan kondisi 'good'
-   **Stok Buruk**: Bad_quantity dari batch dengan kondisi 'bad'
-   **Stok Paket**: Dihitung dari minimum komponen berdasarkan rasio
-   **Stok Tidak Layak Pakai**: Tidak dihitung dalam total stok

### 5.3 Filter dan Periode

-   **Default Period**: Bulan berjalan
-   **Maximum Period**: Tidak ada batasan
-   **Saluran Penjualan**: Jika tidak dipilih, menampilkan semua saluran

## 6. Validasi dan Kontrol

### 6.1 Validasi Akses

-   Sistem memverifikasi role user sebelum menampilkan widget dan filter
-   Packing admin hanya melihat widget stok hampir habis dan akan kedaluwarsa
-   Packing admin tidak melihat widget finansial (revenue, profit, grafik penjualan)
-   Packing admin tidak melihat filter dashboard

### 6.2 Validasi Data

-   Hanya data dengan status approved yang ditampilkan dalam statistik
-   Stok negatif tidak ditampilkan dalam widget stok rendah
-   Batch dengan quantity 0 tidak ditampilkan dalam widget kadaluarsa

## 7. Outcome dan Manfaat

### 7.1 Untuk Owner

-   Visibilitas penuh terhadap performa bisnis
-   Data untuk pengambilan keputusan strategis
-   Monitoring kesehatan finansial perusahaan

### 7.2 Untuk Store Manager

-   Monitoring operasional harian
-   Analisis tren penjualan
-   Perencanaan inventory dan promosi

### 7.3 Untuk Packing Admin

-   Monitoring stok untuk operasional packing
-   Identifikasi produk yang perlu perhatian khusus
-   Perencanaan kerja berdasarkan kondisi stok

## 8. Integrasi dengan Modul Lain

### 8.1 Product Management

-   Data produk untuk widget stok dan kadaluarsa
-   Informasi paket untuk perhitungan stok

### 8.2 Stock Management

-   Data batch stok untuk semua widget
-   Informasi kadaluarsa dan kondisi stok

### 8.3 Product Out

-   Data penjualan untuk widget finansial dan grafik
-   Status approval untuk validasi data

### 8.4 Sales Channel

-   Data saluran untuk filter dan breakdown analisis
