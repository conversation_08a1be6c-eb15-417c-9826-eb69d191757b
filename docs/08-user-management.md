# Dokumentasi Proses Bisnis - User Management

## 1. <PERSON><PERSON><PERSON> Bisnis

User Management mengelola akun karyawan dan sistem role-based access control untuk memastikan keamanan sistem dan pembagian tanggung jawab yang tepat. Sistem ini mengimplementasikan hierarki organisasi dalam akses dan approval workflow.

## 2. Struktur Role dan Hierarki

### 2.1 Role Hierarchy
```
Owner (Pemilik)
    ↓
Store Manager (Kepala Toko)
    ↓
Packing Admin (Admin Packing)
```

### 2.2 Role Definitions

#### 2.2.1 Owner
**Tanggung Jawab**:
- Strategic decision making
- Final approval untuk semua transaksi
- Full access ke semua data dan fitur
- User management dan system configuration

**Akses Sistem**:
- Semua modul dan fitur
- Semua data finansial dan operasional
- Master data management
- User management
- Final approval authority

#### 2.2.2 Store Manager (Kepala Toko)
**Tanggung Jawab**:
- Daily operations management
- First-level approval untuk transaksi
- Staff supervision dan coordination
- Inventory planning dan control

**Aks<PERSON>**:
- Semua modul operasional
- Data finansial dan pricing
- Master data management
- First-level approval authority
- Dapat melihat semua records

#### 2.2.3 Packing Admin (Admin Packing)
**Tanggung Jawab**:
- Data entry untuk transaksi
- Physical inventory handling
- Basic operational tasks
- Documentation dan record keeping

**Akses Sistem**:
- Terbatas pada operational modules
- Tidak dapat akses master data
- Tidak dapat melihat pricing information
- Hanya dapat melihat records sendiri
- Tidak memiliki approval authority

## 3. Permission Matrix

### 3.1 Module Access

| Module | Owner | Store Manager | Packing Admin |
|--------|-------|---------------|---------------|
| Dashboard | Full | Full | Limited* |
| Product Management | Full | Full | No Access |
| Stock Management | Full | Full | Limited** |
| Product In | Full | Full | Limited*** |
| Product Out | Full | Full | Limited*** |
| Returns Management | Full | Full | Limited |
| Opname Management | Full | Full | Limited |
| User Management | Full | No Access | No Access |
| Master Data | Full | Full | No Access |

*Limited: Tidak dapat melihat financial widgets
**Limited: Tidak dapat melihat pricing dan stock value
***Limited: Hanya records sendiri, tidak dapat approval

### 3.2 Data Visibility

| Data Type | Owner | Store Manager | Packing Admin |
|-----------|-------|---------------|---------------|
| Purchase Price | ✓ | ✓ | ✗ |
| Sale Price | ✓ | ✓ | ✗ |
| Profit Information | ✓ | ✓ | ✗ |
| Stock Value | ✓ | ✓ | ✗ |
| All Records | ✓ | ✓ | Own Only |
| Financial Reports | ✓ | ✓ | ✗ |

### 3.3 Action Permissions

| Action | Owner | Store Manager | Packing Admin |
|--------|-------|---------------|---------------|
| Create Records | ✓ | ✓ | ✓ |
| Edit Records | ✓ | ✓ | Own Only |
| Delete Records | ✓ | ✓ | ✗ |
| Approve Transactions | ✓ | ✓ | ✗ |
| Manage Users | ✓ | ✗ | ✗ |
| Manage Master Data | ✓ | ✓ | ✗ |

## 4. Workflow User Management

### 4.1 Pembuatan User Baru
1. **Akses User Management** (Owner only):
   - Masuk ke menu Employee/User Management
   - Klik "Create Employee"

2. **Input Data User**:
   - Nama lengkap karyawan
   - Email address (unique)
   - Password initial
   - Pilih role (Owner/Store Manager/Packing Admin)

3. **Validasi Data**:
   - Email harus unique di sistem
   - Password harus memenuhi requirement
   - Role harus dipilih

4. **Account Creation**:
   - User account dibuat dengan role yang dipilih
   - Email confirmation (opsional)
   - Initial login instructions

### 4.2 Pengelolaan User Existing
1. **Edit User Information**:
   - Update nama, email, atau role
   - Reset password jika diperlukan
   - Activate/deactivate account

2. **Role Changes**:
   - Perubahan role mempengaruhi akses langsung
   - Validation untuk role hierarchy
   - Audit trail untuk perubahan role

3. **Account Maintenance**:
   - Password reset
   - Account lockout management
   - Session management

## 5. Security Implementation

### 5.1 Authentication
- **Email-based login**: Menggunakan email sebagai username
- **Password hashing**: Secure password storage
- **Session management**: Secure session handling
- **Remember me**: Optional persistent login

### 5.2 Authorization
- **Role-based access**: Akses berdasarkan role
- **Method-level security**: Protection di level method
- **Resource-level security**: Protection di level resource
- **Dynamic permission checking**: Real-time permission check

### 5.3 Data Segregation
- **Record ownership**: Packing admin hanya akses record sendiri
- **Pricing visibility**: Hanya authorized roles
- **Financial data**: Restricted access
- **Master data**: Limited access

## 6. Business Rules

### 6.1 Role Assignment Rules
- **Single role per user**: Setiap user hanya memiliki satu role
- **Role hierarchy**: Tidak dapat assign role lebih tinggi
- **Minimum access**: Setiap role memiliki minimum access
- **Role consistency**: Role harus konsisten dengan tanggung jawab

### 6.2 Access Control Rules
- **Own records only**: Packing admin hanya akses record sendiri
- **Pricing restriction**: Pricing hanya untuk authorized users
- **Master data restriction**: Master data hanya untuk management
- **Approval authority**: Sesuai dengan role hierarchy

### 6.3 Data Visibility Rules
- **Financial data**: Hanya untuk management roles
- **All records**: Hanya untuk management roles
- **Sensitive information**: Role-based visibility
- **Audit information**: Restricted access

## 7. Audit dan Compliance

### 7.1 User Activity Logging
- **Login/logout tracking**: Mencatat aktivitas login
- **Action logging**: Mencatat semua action user
- **Data access logging**: Mencatat akses ke data sensitif
- **Permission changes**: Mencatat perubahan permission

### 7.2 Compliance Features
- **Password policy**: Enforcement password policy
- **Session timeout**: Automatic session timeout
- **Account lockout**: Protection dari brute force
- **Audit trail**: Complete audit trail

### 7.3 Security Monitoring
- **Failed login attempts**: Monitoring login failures
- **Suspicious activities**: Detection aktivitas mencurigakan
- **Permission violations**: Monitoring violation attempts
- **Data access patterns**: Monitoring access patterns

## 8. Integration dengan Modul Lain

### 8.1 Transaction Modules
- **User tracking**: Semua transaksi mencatat user
- **Approval workflow**: Berdasarkan user role
- **Data visibility**: Sesuai dengan user permission
- **Edit restrictions**: Berdasarkan user dan record ownership

### 8.2 Master Data Modules
- **Access control**: Hanya authorized users
- **Change tracking**: Mencatat siapa yang mengubah
- **Approval requirement**: Untuk perubahan critical
- **Data integrity**: Memastikan integritas data

### 8.3 Reporting Modules
- **Data filtering**: Berdasarkan user permission
- **Financial reports**: Restricted access
- **User-specific reports**: Sesuai dengan role
- **Audit reports**: Untuk compliance

## 9. Error Handling dan Security

### 9.1 Access Denied Handling
- **403 Forbidden**: Untuk unauthorized access
- **Clear error messages**: Pesan error yang informatif
- **Graceful degradation**: Fallback untuk restricted features
- **User guidance**: Petunjuk untuk proper access

### 9.2 Security Measures
- **Input validation**: Validasi semua input user
- **SQL injection prevention**: Protection dari SQL injection
- **XSS prevention**: Protection dari XSS attacks
- **CSRF protection**: Protection dari CSRF attacks

### 9.3 Data Protection
- **Sensitive data masking**: Masking data sensitif
- **Encryption**: Encryption untuk data critical
- **Secure transmission**: HTTPS untuk semua komunikasi
- **Data backup**: Regular backup dengan security

## 10. User Experience

### 10.1 Role-Appropriate Interface
- **Dynamic menus**: Menu sesuai dengan role
- **Feature visibility**: Fitur sesuai dengan permission
- **Contextual help**: Help sesuai dengan role
- **Simplified workflow**: Workflow sesuai dengan tanggung jawab

### 10.2 Notification System
- **Role-based notifications**: Notifikasi sesuai dengan role
- **Approval notifications**: Untuk approval workflow
- **Security alerts**: Untuk security events
- **System announcements**: Untuk system updates

### 10.3 Dashboard Customization
- **Role-specific widgets**: Widget sesuai dengan role
- **Data filtering**: Filter sesuai dengan permission
- **Performance metrics**: Metrics sesuai dengan tanggung jawab
- **Quick actions**: Actions sesuai dengan role

## 11. Outcome dan Manfaat

### 11.1 Untuk Security
- **Access control**: Kontrol akses yang ketat
- **Data protection**: Perlindungan data sensitif
- **Audit compliance**: Memenuhi requirement audit
- **Risk mitigation**: Mitigasi risk security

### 11.2 Untuk Operations
- **Clear responsibility**: Pembagian tanggung jawab yang jelas
- **Efficient workflow**: Workflow yang efisien
- **Accountability**: Akuntabilitas yang tinggi
- **Performance tracking**: Tracking performance user

### 11.3 Untuk Management
- **Control mechanism**: Mekanisme kontrol yang efektif
- **Visibility**: Visibility yang sesuai dengan level
- **Decision support**: Support untuk decision making
- **Compliance assurance**: Jaminan compliance
