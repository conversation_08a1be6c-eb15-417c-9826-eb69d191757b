# Sistem Reservasi Stock Batch

## Overview

Sistem reservasi stock batch memungkinkan produk keluar untuk "mereservasi" stock batch berdasarkan aturan FEFO (First Expired First Out) dan <PERSON> (Landed Cost Optimization First) sejak saat dibuat oleh admin, dan akan diupdate ketika ada edit sampai sebelum status disetujui.

## Komponen Utama

### 1. Database Schema

#### Tabel `stock_reservations`

```sql
- id (PK)
- product_out_id (FK → product_outs.id)
- product_out_item_id (FK → product_out_items.id)
- batch_id (FK → stock_batches.id)
- quantity (reserved quantity)
- stock_condition (normal/bad)
- reserved_at (timestamp)
- expires_at (nullable - untuk auto-cleanup)
```

### 2. Model dan Relasi

#### StockReservation Model

-   Relasi ke ProductOut, ProductOutItem, dan StockBatch
-   Scope untuk active reservations
-   Helper methods untuk check status

#### Relasi <PERSON>bahan

-   ProductOut::stockReservations()
-   ProductOutItem::stockReservations()
-   StockBatch::stockReservations()

### 3. Services

#### StockReservationService

-   `createReservationsForProductOut()` - Buat reservasi untuk semua items
-   `createReservationForItem()` - Buat reservasi untuk single item
-   `clearReservationsForProductOut()` - Hapus semua reservasi
-   `convertReservationsToStockMovements()` - Convert ke actual stock movement saat approved

#### AvailableStockService

-   `getAvailableNormalStock()` - Hitung available normal stock (exclude reserved)
-   `getAvailableBadStock()` - Hitung available bad stock (exclude reserved)
-   `getStockSummary()` - Summary lengkap stock dan reservasi

## Workflow

### 1. Saat ProductOut Dibuat

```php
// Di CreateProductOut::handleRecordCreation()
$stockService = new ProductOutStockService();
$stockService->processNewProductOut($record);

// Yang akan memanggil:
if ($productOut->isApproved()) {
    // Langsung proses stock (owner)
    $this->processApprovedProductOut($productOut);
} else {
    // Buat reservasi (admin/manager)
    $this->createReservationsForNewProductOut($productOut);
}
```

### 2. Saat ProductOut Diedit

```php
// Di EditProductOut::afterSave()
if ($record->isApproved()) {
    // Reprocess stock
    $stockService->reprocessStockForEditedProductOut($record);
} else {
    // Update reservasi
    $stockService->updateReservationsForEditedProductOut($record);
}
```

### 3. Saat Status Berubah ke Approved

```php
// Di ProductOutObserver::updated()
if ($productOut->status === ProductOutStatus::APPROVED) {
    $this->stockService->processApprovedProductOut($productOut);
}

// Yang akan memanggil:
$this->reservationService->convertReservationsToStockMovements($productOut);
```

### 4. Saat Status Berubah ke Rejected

```php
// Di ProductOutObserver::updated()
if ($productOut->status === ProductOutStatus::REJECTED) {
    $this->stockService->restoreStockForRejectedProductOut($productOut);
}

// Yang akan memanggil:
$this->reservationService->clearReservationsForProductOut($productOut);
```

## Logika FEFO dan LCOF

### Normal Stock

```php
$availableBatches = StockBatch::where('product_id', $productId)
    ->where('quantity', '>', 0)
    ->orderBy('expiration_date', 'asc')  // FEFO
    ->orderBy('purchase_price', 'asc')   // LCOF
    ->get();
```

### Bad Stock

```php
$availableBatches = StockBatch::where('product_id', $productId)
    ->where('bad_quantity', '>', 0)
    ->orderBy('expiration_date', 'asc')  // FEFO
    ->orderBy('purchase_price', 'asc')   // LCOF
    ->get();
```

### Available Stock Calculation

```php
$availableInBatch = $batch->quantity - $this->getReservedQuantity($batch->id, 'normal');
```

## Package Products

Untuk produk paket, sistem akan:

1. Breakdown package menjadi komponen
2. Buat reservasi untuk setiap komponen berdasarkan komposisi dan kondisi stock
3. Gunakan FEFO/LCOF untuk setiap komponen sesuai kondisi stock

```php
foreach ($packageProduct->packageCompositions as $composition) {
    $requiredQuantity = $composition->quantity * $packageQuantity;
    $stockCondition = $composition->stock_condition === 'bad' ? 'bad' : 'normal';
    $this->createComponentReservation($item, $componentProduct, $requiredQuantity, $stockCondition);
}
```

## UI Integration

### Form Dropdown

-   Menampilkan available stock (exclude reserved)
-   Format: "Product Name (Tersedia: X)"

### Relation Manager

-   Kolom "Info Batch" menampilkan status reservasi
-   Format: "Batch #X (Y unit - Reserved/Processed)"

### Available Stock Display

```php
// Di dropdown options
$availableStock = $availableStockService->getAvailableNormalStock($product->id);
$options["normal_{$product->id}"] = "{$product->name} (Tersedia: {$availableStock})";
```

## Testing

### Manual Testing

```bash
php artisan db:seed --class=StockReservationTestSeeder
```

### Test Scenarios

1. **Create ProductOut** - Reservasi otomatis dibuat
2. **Edit ProductOut** - Reservasi diupdate
3. **Approve ProductOut** - Reservasi dikonversi ke stock movement
4. **Reject ProductOut** - Reservasi dihapus
5. **Available Stock** - Dropdown menampilkan stock yang benar

## Error Handling

### Stock Tidak Cukup

```php
if ($totalAvailable < $requiredQuantity) {
    throw new \Exception("Stock tidak cukup untuk produk {$productName}. Tersedia: {$totalAvailable}, dibutuhkan: {$requiredQuantity}");
}
```

### Rollback pada Error

-   Semua operasi reservasi dibungkus dalam DB transaction
-   Jika ada error, semua reservasi di-rollback

## Performance Considerations

### Database Indexes

```sql
INDEX (product_out_id, batch_id)
INDEX (batch_id, stock_condition)
INDEX (expires_at)
```

### Query Optimization

-   Eager loading relasi yang diperlukan
-   Batch operations untuk multiple reservations
-   Caching untuk available stock calculation

## Monitoring dan Logging

### Log Events

-   Reservasi dibuat/diupdate/dihapus
-   Konversi reservasi ke stock movement
-   Error dalam proses reservasi

### Metrics

-   Total reservasi aktif
-   Stock yang direservasi per produk
-   Performance query available stock

## Future Enhancements

1. **Auto-cleanup expired reservations**
2. **Reservation priority system**
3. **Partial reservation release**
4. **Advanced reservation analytics**
5. **Reservation conflict detection**
