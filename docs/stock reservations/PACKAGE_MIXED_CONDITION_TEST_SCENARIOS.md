# Package Mixed Condition Test Scenarios

## Bug Fix Verification Tests

### Test Case 1: Same Product with Different Conditions
**Scenario**: Paket berisi produk yang sama dengan kondisi berbeda
```
Package: Paket A
Components:
- Produk X (baik) - 2 pcs
- Produk X (buruk) - 1 pcs
```

**Expected Behavior**:
- Reservasi 1: 2 pcs dari stock baik Produk X
- Reservasi 2: 1 pcs dari stock buruk Produk X

**Before Fix**: Membuat 3 pcs reservasi dari stock baik saja
**After Fix**: Membuat reservasi sesuai kondisi masing-masing

### Test Case 2: Different Products with Bad Condition
**Scenario**: Paket berisi produk berbeda dengan kondisi buruk
```
Package: Paket B
Components:
- Produk Y (buruk) - 1 pcs
- Produk Z (buruk) - 2 pcs
```

**Expected Behavior**:
- Reservasi 1: 1 pcs dari stock buruk Produk Y
- Reservasi 2: 2 pcs dari stock buruk Produk Z

**Status**: ✅ Sudah benar sebelumnya (produk berbeda)

### Test Case 3: Mixed Conditions Multiple Products
**Scenario**: Paket berisi berbagai kombinasi
```
Package: Paket C
Components:
- Produk A (baik) - 1 pcs
- Produk A (buruk) - 1 pcs
- Produk B (baik) - 2 pcs
- Produk C (buruk) - 1 pcs
```

**Expected Behavior**:
- Reservasi 1: 1 pcs dari stock baik Produk A
- Reservasi 2: 1 pcs dari stock buruk Produk A
- Reservasi 3: 2 pcs dari stock baik Produk B
- Reservasi 4: 1 pcs dari stock buruk Produk C

### Test Case 4: Insufficient Stock Error Handling
**Scenario**: Stock tidak mencukupi untuk kondisi tertentu
```
Package: Paket D
Components:
- Produk X (baik) - 5 pcs (tersedia: 3 pcs)
- Produk X (buruk) - 2 pcs (tersedia: 2 pcs)
```

**Expected Behavior**:
- Error: "Stock baik tidak cukup untuk komponen Produk X. Tersedia: 3, dibutuhkan: 5"

## Code Changes Verification

### 1. StockReservationService::createReservationForPackageComponent()
```php
// ✅ Fixed: Now uses composition stock condition
$stockCondition = $composition->stock_condition === 'bad' ? 'bad' : 'normal';
$componentReservations = $this->createComponentReservation(
    $item, $componentProduct, $requiredQuantity, $stockCondition
);
```

### 2. StockReservationService::createComponentReservation()
```php
// ✅ Fixed: Now selects correct batches based on condition
if ($stockCondition === 'bad') {
    $availableBatches = $this->getAvailableBadStockBatches($component->id);
} else {
    $availableBatches = $this->getAvailableNormalStockBatches($component->id);
}
```

### 3. Error Message Improvement
```php
// ✅ Enhanced: More specific error messages
$conditionLabel = $stockCondition === 'bad' ? 'buruk' : 'baik';
throw new \Exception("Stock {$conditionLabel} tidak cukup untuk komponen {$component->name}");
```

## Manual Testing Steps

### Step 1: Setup Test Data
1. Create products with both good and bad stock
2. Create package with mixed conditions
3. Ensure sufficient stock for positive tests
4. Ensure insufficient stock for negative tests

### Step 2: Create Product Out
1. Add package to product out form
2. Submit form
3. Check stock reservations created

### Step 3: Verify Reservations
1. Check `stock_reservations` table
2. Verify `stock_condition` field values
3. Verify `batch_id` points to correct stock type
4. Verify quantities match composition requirements

### Step 4: Test Error Scenarios
1. Try creating product out with insufficient good stock
2. Try creating product out with insufficient bad stock
3. Verify error messages are specific to condition

## Database Queries for Verification

### Check Reservations by Condition
```sql
SELECT 
    sr.id,
    sr.quantity,
    sr.stock_condition,
    p.name as product_name,
    sb.quantity as batch_good_stock,
    sb.bad_quantity as batch_bad_stock
FROM stock_reservations sr
JOIN stock_batches sb ON sr.batch_id = sb.id
JOIN products p ON sb.product_id = p.id
WHERE sr.product_out_id = [PRODUCT_OUT_ID]
ORDER BY p.name, sr.stock_condition;
```

### Check Package Compositions
```sql
SELECT 
    pc.package_product_id,
    pc.component_product_id,
    pc.quantity,
    pc.stock_condition,
    p1.name as package_name,
    p2.name as component_name
FROM package_compositions pc
JOIN products p1 ON pc.package_product_id = p1.id
JOIN products p2 ON pc.component_product_id = p2.id
WHERE pc.package_product_id = [PACKAGE_ID];
```

## Expected Results Summary

| Test Case | Before Fix | After Fix | Status |
|-----------|------------|-----------|---------|
| Same product, different conditions | ❌ Wrong reservations | ✅ Correct reservations | Fixed |
| Different products, bad conditions | ✅ Already correct | ✅ Still correct | OK |
| Mixed conditions, multiple products | ❌ Partially wrong | ✅ All correct | Fixed |
| Insufficient stock errors | ❌ Generic messages | ✅ Specific messages | Enhanced |

## Regression Testing

Ensure these scenarios still work correctly:
- ✅ Single products (non-package)
- ✅ Packages with all good conditions
- ✅ Packages with all bad conditions
- ✅ Normal stock FEFO/LCOF logic
- ✅ Bad stock FEFO logic
- ✅ Stock movement conversion after approval
