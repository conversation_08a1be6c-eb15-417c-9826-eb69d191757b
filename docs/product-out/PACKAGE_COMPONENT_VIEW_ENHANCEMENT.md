# Package Component View Enhancement - ProductOut Relation Manager

## Overview

Dokumentasi ini menjelaskan rencana enhancement untuk mempertahankan dan meningkatkan tampilan detail komponen paket di ProductOut relation manager, terutama untuk status rejected yang saat ini tidak menampilkan breakdown komponen dengan detail yang memadai.

## Current State Analysis

### Masalah yang Sudah Diperbaiki ✅
- **Relation manager tidak menampilkan data saat rejected**: Sudah diperbaiki dengan menambahkan handling `$productOut->isRejected()` di semua logic
- **Query logic untuk status rejected**: Sudah menggunakan `ProductOutItem` untuk status approved dan rejected

### Masalah yang Masih Ada ❌
- **Detail komponen paket tidak lengkap**: Saat status rejected, detail komponen tidak menampilkan breakdown per batch seperti saat pending
- **Informasi batch per komponen hilang**: Tidak ada informasi batch individual untuk setiap komponen paket
- **Harga per komponen tidak akurat**: Harga dibagi rata tanpa mempertimbangkan batch cost yang berbeda

## Current Behavior vs Expected Behavior

### Status: PENDING (Stock Reservations)
**Current Behavior** ✅ **Working Well**
```
📦 Paket Vitamin A (2 unit)
├── └─ Vitamin A 100mg (Batch #123, Exp: 15 Jan 2025, Qty: 3)
├── └─ Vitamin A 100mg (Batch #124, Exp: 20 Jan 2025, Qty: 2)
└── └─ Vitamin A 200mg (Batch #125, Exp: 18 Jan 2025, Qty: 5)
```

### Status: APPROVED ✅ **Working Well**
**Current Behavior**
```
📦 Paket Vitamin A (2 unit)
├── └─ Vitamin A 100mg (Batch #123, Qty: 3, Harga: Rp 15.000)
├── └─ Vitamin A 100mg (Batch #124, Qty: 2, Harga: Rp 15.000)  
└── └─ Vitamin A 200mg (Batch #125, Qty: 5, Harga: Rp 15.000)
```

### Status: REJECTED ❌ **Needs Enhancement**
**Current Behavior** (Setelah fix)
```
📦 Paket Vitamin A (2 unit) - Single record, no component breakdown
```

**Expected Behavior** (Target Enhancement)
```
📦 Paket Vitamin A (2 unit)
├── └─ Vitamin A 100mg (Batch #123, Qty: 3, Harga: Rp 15.000) [REJECTED]
├── └─ Vitamin A 100mg (Batch #124, Qty: 2, Harga: Rp 15.000) [REJECTED]
└── └─ Vitamin A 200mg (Batch #125, Qty: 5, Harga: Rp 15.000) [REJECTED]
```

## Technical Analysis

### Data Flow Comparison

#### PENDING Status
```
ProductOut (PENDING)
├── ProductOutItem (Package info only)
└── StockReservation (Multiple records per batch)
    ├── Batch A (Component 1)
    ├── Batch B (Component 1) 
    └── Batch C (Component 2)
```

#### APPROVED Status  
```
ProductOut (APPROVED)
└── ProductOutItem (Multiple records per batch)
    ├── Item A (Component 1, Batch A)
    ├── Item B (Component 1, Batch B)
    └── Item C (Component 2, Batch C)
```

#### REJECTED Status (Current)
```
ProductOut (REJECTED)
└── ProductOutItem (Original package record only)
    └── Single record with package info
```

#### REJECTED Status (Target)
```
ProductOut (REJECTED)
└── ProductOutItem (Multiple records preserved)
    ├── Item A (Component 1, Batch A) [REJECTED]
    ├── Item B (Component 1, Batch B) [REJECTED]
    └── Item C (Component 2, Batch C) [REJECTED]
```

## Root Cause Analysis

### Why Component Details Are Lost on Rejection

1. **Stock Reservation Deletion**: `ProductOutObserver` menghapus `StockReservation` saat rejection
2. **No Component Conversion**: Tidak ada proses konversi dari `StockReservation` ke `ProductOutItem` saat rejection
3. **Original Package Record Only**: Hanya `ProductOutItem` original (package level) yang tersisa

### Current Observer Behavior
```php
// ProductOutObserver.php - Line 37-48
if ($productOut->status === ProductOutStatus::REJECTED) {
    // Kembalikan stock jika ada reservasi
    if ($this->stockService->hasStockToRestore($productOut)) {
        $this->stockService->restoreStockForRejectedProductOut($productOut);
    }
    // ❌ StockReservation dihapus, tidak ada konversi ke ProductOutItem
}
```

## Enhancement Plan

### Phase 1: Data Preservation Strategy

#### Option A: Preserve StockReservations for Rejected Status
**Pros:**
- Minimal code changes
- Existing relation manager logic works
- Historical data preserved

**Cons:**
- Inconsistent data model (approved uses ProductOutItem, rejected uses StockReservation)
- Complex query logic maintenance

#### Option B: Convert StockReservations to ProductOutItems on Rejection ⭐ **RECOMMENDED**
**Pros:**
- Consistent data model across all statuses
- Better audit trail
- Simpler relation manager logic

**Cons:**
- More complex implementation
- Need to handle price division and batch assignment

### Phase 2: Implementation Strategy

#### Step 1: Enhance ProductOutObserver
```php
// New method in ProductOutObserver
private function preserveComponentDetailsOnRejection(ProductOut $productOut): void
{
    // Convert StockReservations to ProductOutItems before deletion
    $this->stockReservationService->convertReservationsToRejectedItems($productOut);
}
```

#### Step 2: Create StockReservationService Method
```php
// New method in StockReservationService
public function convertReservationsToRejectedItems(ProductOut $productOut): void
{
    // Similar to convertReservationsToStockMovements but for rejected status
    // Create ProductOutItems with proper batch assignment and price division
    // Mark items with rejection status indicator
}
```

#### Step 3: Enhance ProductOutItem Model
```php
// Add rejection tracking
protected $fillable = [
    // ... existing fields
    'is_rejected',
    'rejection_timestamp',
];
```

#### Step 4: Update Relation Manager Display
```php
// Enhanced component info display
->state(function ($record) use ($productOut) {
    if ($productOut->isRejected() && $record->is_rejected) {
        $product = $record->stockBatch ? $record->stockBatch->product : null;
        $status = " [DITOLAK]";
        return $product ? "└─ {$product->name}{$status}" : "└─ Komponen Tidak Diketahui{$status}";
    }
    // ... existing logic
})
```

## Implementation TODO

### High Priority 🔴

1. **[ ] Enhance ProductOutObserver**
   - [ ] Add `preserveComponentDetailsOnRejection()` method
   - [ ] Call method before stock restoration in rejection handler
   - [ ] Add proper logging for component preservation

2. **[ ] Create StockReservationService Enhancement**
   - [ ] Add `convertReservationsToRejectedItems()` method
   - [ ] Handle price division for package components
   - [ ] Preserve batch assignment and quantities
   - [ ] Add rejection metadata to created items

3. **[ ] Database Schema Updates**
   - [ ] Add `is_rejected` boolean column to `product_out_items` table
   - [ ] Add `rejection_timestamp` timestamp column
   - [ ] Create migration for existing rejected records

### Medium Priority 🟡

4. **[ ] Enhance ProductOutItem Model**
   - [ ] Add fillable fields for rejection tracking
   - [ ] Add scope for rejected items
   - [ ] Add helper methods for rejection status

5. **[ ] Update Relation Manager Display**
   - [ ] Add rejection status indicators in component info
   - [ ] Add visual distinction for rejected items (badges, colors)
   - [ ] Add filter for rejected components

6. **[ ] Testing & Validation**
   - [ ] Test package rejection with multiple batches
   - [ ] Verify price calculations remain accurate
   - [ ] Test relation manager display for all statuses

### Low Priority 🟢

7. **[ ] UI/UX Enhancements**
   - [ ] Add rejection reason display per component
   - [ ] Add rejection timestamp in component details
   - [ ] Add export functionality for rejected component details

8. **[ ] Performance Optimization**
   - [ ] Optimize queries for rejected item display
   - [ ] Add caching for component calculations
   - [ ] Add database indexes for rejection queries

9. **[ ] Documentation & Training**
   - [ ] Update user documentation
   - [ ] Create training materials for rejection workflow
   - [ ] Document new database schema

## Migration Strategy

### For Existing Rejected Records

```php
// Migration to handle existing rejected ProductOuts
public function up()
{
    // Add new columns
    Schema::table('product_out_items', function (Blueprint $table) {
        $table->boolean('is_rejected')->default(false);
        $table->timestamp('rejection_timestamp')->nullable();
    });
    
    // Mark existing items from rejected ProductOuts
    DB::table('product_out_items')
        ->whereIn('product_out_id', function ($query) {
            $query->select('id')
                  ->from('product_outs')
                  ->where('status', 'rejected');
        })
        ->update([
            'is_rejected' => true,
            'rejection_timestamp' => DB::raw('updated_at')
        ]);
}
```

## Success Criteria

### Functional Requirements ✅
- [ ] Rejected ProductOuts show component breakdown in relation manager
- [ ] Each component shows correct batch information
- [ ] Price calculations remain accurate for rejected items
- [ ] Historical data is preserved for audit purposes

### Technical Requirements ✅
- [ ] Consistent data model across all ProductOut statuses
- [ ] No performance degradation in relation manager
- [ ] Proper error handling for edge cases
- [ ] Backward compatibility with existing data

### User Experience Requirements ✅
- [ ] Clear visual indication of rejection status
- [ ] Easy filtering between approved and rejected components
- [ ] Intuitive component grouping and display
- [ ] Consistent behavior across different ProductOut statuses

## Risk Assessment

### High Risk 🔴
- **Data Loss**: Improper handling during conversion could lose component details
- **Performance Impact**: Additional ProductOutItems could slow down queries

### Medium Risk 🟡  
- **Price Calculation Errors**: Complex price division logic might introduce bugs
- **UI Confusion**: Too much information might overwhelm users

### Low Risk 🟢
- **Migration Complexity**: Existing data migration is straightforward
- **Testing Overhead**: Additional test cases needed but manageable

## Next Steps

1. **Review and Approve Plan**: Stakeholder review of enhancement strategy
2. **Create Detailed Technical Specs**: Break down each TODO into specific tasks
3. **Set Implementation Timeline**: Prioritize based on business needs
4. **Begin Phase 1 Implementation**: Start with data preservation strategy
5. **Iterative Testing**: Test each component before moving to next phase

---

**Last Updated**: 02 Jun 2025  
**Status**: Planning Phase  
**Priority**: Medium  
**Estimated Effort**: 2-3 weeks development + 1 week testing
