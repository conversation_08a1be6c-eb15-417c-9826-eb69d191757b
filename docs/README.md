# Dokumentasi Proses Bisnis - Sistem Inventory Management

## Daftar Isi

Dokumentasi ini menjelaskan proses bisnis untuk setiap menu dan modul dalam sistem inventory management. Setiap dokumentasi fokus pada workflow, business rules, role & permission, dan integrasi antar modul.

### 📊 [01. Dashboard](./01-dashboard.md)
**Tujuan**: Pusat informasi dan monitoring real-time kondisi bisnis dan operasional gudang

**Fitur Utama**:
- Widget statistik penjualan (revenue, profit, total order)
- Grafik penjualan dengan multiple time periods
- Monitoring stok rendah dan mendekati kadaluarsa
- Filter berdasarkan periode dan saluran penjualan

**Role Access**: Semua role (dengan pembatasan data finansial untuk packing admin)

---

### 📦 [02. Product Management](./02-product-management.md)
**Tujuan**: Mengelola master data produk (single dan package) serta komposisi paket

**Fitur Utama**:
- Manajemen produk tunggal dan produk paket
- Package composition dengan komponen dan rasio
- Prefix notation untuk identifikasi jenis produk
- Perhitungan stok paket otomatis dari komponen

**Role Access**: Owner dan Store Manager only

---

### 📋 [03. Stock Management](./03-stock-management.md)
**Tujuan**: Monitoring dan pengelolaan stok dengan berbagai kondisi (good, bad, unusable)

**Fitur Utama**:
- Tracking stok per batch dengan FEFO logic
- Manajemen kondisi stok (normal, buruk, tidak layak pakai)
- Monitoring kadaluarsa dan nilai stok
- Stock movement history dan audit trail

**Role Access**: Semua role (dengan pembatasan pricing untuk packing admin)

---

### 📥 [04. Product In (Penerimaan Barang)](./04-product-in.md)
**Tujuan**: Mengelola penerimaan barang dari supplier dengan sistem approval bertingkat

**Fitur Utama**:
- Approval workflow: Packing Admin → Store Manager → Owner
- Photo evidence dan documentation
- Package breakdown otomatis ke komponen
- Stock batch creation setelah final approval

**Role Access**: Semua role dengan approval authority sesuai hierarki

---

### 📤 [05. Product Out (Penjualan/Distribusi)](./05-product-out.md)
**Tujuan**: Mengelola penjualan dan distribusi dengan stock reservation dan FEFO+LCOF logic

**Fitur Utama**:
- Stock reservation saat create dengan FEFO+LCOF
- Approval workflow dengan stock deduction setelah final approval
- Sistem diskon dan profit calculation
- Handling produk paket dengan component breakdown

**Role Access**: Semua role dengan approval authority sesuai hierarki

---

### 🔄 [06. Returns Management](./06-returns-management.md)
**Tujuan**: Mengelola pengembalian produk dengan fleksibilitas kondisi return

**Fitur Utama**:
- Return dengan kondisi berbeda dari kondisi asal
- Package return per komponen individual
- Stock restoration sesuai kondisi return
- Return reason tracking dan analytics

**Role Access**: Semua role (dengan pembatasan pricing untuk packing admin)

---

### 📊 [07. Opname Management](./07-opname-management.md)
**Tujuan**: Mengelola stock opname untuk memastikan akurasi stok sistem vs fisik

**Fitur Utama**:
- Physical count per batch (expiration + price)
- Variance analysis dan stock adjustment
- Multiple opname types (full, partial, cycle count)
- Audit trail dan compliance reporting

**Role Access**: Semua role dengan pembatasan delete untuk packing admin

---

### 👥 [08. User Management](./08-user-management.md)
**Tujuan**: Mengelola user accounts dan role-based access control

**Fitur Utama**:
- 3-tier role system (Owner, Store Manager, Packing Admin)
- Granular permission matrix
- Data segregation dan access control
- Security dan audit features

**Role Access**: Owner only

---

### ✅ [09. Approval System](./09-approval-system.md)
**Tujuan**: Sistem persetujuan bertingkat untuk kontrol kualitas dan akuntabilitas

**Fitur Utama**:
- Sequential approval workflow
- Status tracking dan notification system
- Stock processing integration
- Audit trail dan compliance

**Role Access**: Approval authority sesuai hierarki role

---

### 🗂️ [10. Master Data Management](./10-master-data-management.md)
**Tujuan**: Mengelola data referensi utama (Brand, Supplier, Sales Channel, Discount)

**Fitur Utama**:
- Brand, Supplier, Sales Channel management
- Discount rules dan pricing strategy
- Data integrity dan validation
- Integration dengan semua modul operasional

**Role Access**: Owner dan Store Manager only

---

## Struktur Role dan Permission

### 🏢 Owner
- **Akses**: Semua modul dan fitur
- **Authority**: Final approval untuk semua transaksi
- **Data**: Akses penuh termasuk finansial dan pricing
- **Management**: User management dan system configuration

### 👨‍💼 Store Manager (Kepala Toko)
- **Akses**: Semua modul operasional dan master data
- **Authority**: First-level approval dan full operational control
- **Data**: Akses penuh termasuk finansial dan pricing
- **Management**: Operational management dan staff coordination

### 📦 Packing Admin (Admin Packing)
- **Akses**: Modul operasional terbatas
- **Authority**: Data entry dan basic operations
- **Data**: Tidak dapat akses pricing dan financial data
- **Scope**: Hanya dapat melihat dan edit record sendiri

## Approval Workflow

```
Packing Admin → Store Manager → Owner
     ↓              ↓           ↓
   Create        1st Level   Final Level
   Submit        Approval    Approval
```

**Modul dengan Approval**:
- Product In: Stock batches dibuat setelah owner approval
- Product Out: Stock dikurangi setelah owner approval dengan reservasi system

## Business Rules Utama

### Stock Management
- **FEFO Logic**: First Expired First Out untuk optimalisasi inventory
- **LCOF Logic**: Lowest Cost First untuk cost optimization
- **Condition Tracking**: Good, Bad, Unusable stock dengan logic terpisah
- **Package Logic**: Stok paket dihitung dari minimum komponen

### Approval System
- **Sequential Approval**: Harus berurutan sesuai hierarki
- **Stock Processing**: Hanya setelah approval lengkap
- **Edit Restrictions**: Berdasarkan status dan role
- **Audit Trail**: Complete tracking untuk accountability

### Data Integrity
- **Role-based Access**: Akses sesuai dengan role dan responsibility
- **Data Segregation**: Packing admin hanya akses record sendiri
- **Master Data Control**: Hanya management yang dapat akses
- **Financial Data Protection**: Pricing hanya untuk authorized users

## Integrasi Sistem

Semua modul terintegrasi dengan:
- **Real-time stock updates** berdasarkan transaksi
- **Automatic calculation** untuk stok paket dan finansial
- **Audit trail** untuk semua perubahan data
- **Notification system** untuk approval workflow
- **Role-based data filtering** untuk security

## Teknologi dan Framework

- **Backend**: Laravel dengan Eloquent ORM
- **Frontend**: Filament v3 untuk admin panel
- **Database**: MySQL dengan proper indexing
- **Security**: Role-based access control dengan middleware
- **File Storage**: Laravel storage untuk photo evidence
- **Validation**: Server-side dan client-side validation

---

*Dokumentasi ini dibuat untuk memberikan pemahaman komprehensif tentang proses bisnis sistem inventory management. Setiap file dokumentasi dapat dibaca secara independen atau sebagai bagian dari keseluruhan sistem.*
