# Product In Purchase Price Fix

## Overview
Dokumentasi ini menjelaskan perubahan yang dilakukan untuk mengatasi masalah `SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'purchase_price' cannot be null` yang terjadi ketika kepala toko atau owner membuat produk masuk.

## Problem Statement

### Masalah yang Terjadi
- Error `purchase_price cannot be null` muncul ketika kepala toko/owner membuat produk masuk
- Sistem approval bertingkat: Admin Packing → Kepala Toko → Owner
- Admin packing: harga beli disembunyikan, diisi otomatis oleh sistem
- Kepala toko/owner: bisa melihat dan mengedit harga beli

### Root Cause Analysis
1. **Database Schema**: Kolom `purchase_price` di tabel `product_in_items` didefinisikan sebagai `NOT NULL`
2. **Filament Relationship**: Form menggunakan `->relationship('items')` yang langsung menyimpan ke database
3. **Logic Gap**: `mutateFormDataBeforeCreate` tidak dijalankan untuk relationship items
4. **Frontend Logic**: Auto-fill tidak berjalan dengan benar untuk semua skenario

## Solution Implemented

### Pendekatan yang Dipilih
**Gunakan nilai 0 sebagai fallback** untuk purchase_price daripada null, dengan alasan:
- Menjaga data integrity (NOT NULL constraint)
- Lebih mudah untuk business logic
- Konsisten dengan requirement: "jika default price kosong, isi dengan 0"

### Technical Changes

#### 1. ProductInResource.php
**File**: `app/Filament/Resources/ProductInResource.php`

**Perubahan**:
- Removed `->relationship('items')` untuk manual handling
- Updated auto-fill logic menggunakan `?? 0` fallback
- Improved checkbox logic untuk kepala toko/owner

```php
// Before
if ($product->default_purchase_price) {
    $set('purchase_price', number_format($product->default_purchase_price, 0, ',', '.'));
} else {
    $set('purchase_price', null);
}

// After  
$defaultPrice = $product->default_purchase_price ?? 0;
$set('purchase_price', number_format($defaultPrice, 0, ',', '.'));
```

#### 2. CreateProductIn.php
**File**: `app/Filament/Resources/ProductInResource/Pages/CreateProductIn.php`

**Perubahan**:
- Added `handleRecordCreation()` method untuk manual item creation
- Added `createProductInItems()` method dengan proper fallback logic
- Wrapped dalam DB transaction untuk safety

```php
protected function handleRecordCreation(array $data): Model
{
    return \DB::transaction(function () use ($data) {
        $items = $data['items'] ?? [];
        unset($data['items']);
        
        $data = $this->mutateFormDataBeforeCreate($data);
        $productIn = static::getModel()::create($data);
        $this->createProductInItems($productIn, $items);
        
        return $productIn;
    });
}
```

#### 3. EditProductIn.php
**File**: `app/Filament/Resources/ProductInResource/Pages/EditProductIn.php`

**Perubahan**:
- Updated auto-fill logic dengan `?? 0` fallback
- Updated `reprocessStockBatches()` method dengan proper fallback

### Business Logic Implementation

#### Admin Packing
- ✅ Harga beli tetap hidden
- ✅ Auto-set dengan `default_purchase_price` jika ada
- ✅ **Gunakan 0 jika produk tidak punya default price**
- ✅ Tidak ada error constraint violation

#### Kepala Toko/Owner
- ✅ Checkbox "Gunakan Harga Default Beli" = `true` by default
- ✅ Field disabled ketika checkbox `true`
- ✅ Auto-fill dengan `default_purchase_price ?? 0` ketika checkbox `true`
- ✅ Clear field ketika checkbox `false` untuk input manual
- ✅ Dynamic required validation

## Testing Scenarios

### Test Case 1: Admin Packing
1. **Produk dengan default price**: Harga terisi otomatis dengan default price
2. **Produk tanpa default price**: Harga terisi otomatis dengan 0
3. **Submit form**: Berhasil tanpa error

### Test Case 2: Kepala Toko/Owner
1. **Checkbox true + produk dengan default price**: Auto-fill dengan default price
2. **Checkbox true + produk tanpa default price**: Auto-fill dengan 0
3. **Checkbox false**: Field kosong untuk input manual
4. **Submit dengan harga custom**: Berhasil dengan harga yang diinput
5. **Submit dengan checkbox true**: Berhasil dengan default price atau 0

## Files Modified

1. `app/Filament/Resources/ProductInResource.php`
   - Lines 128-130: Removed relationship
   - Lines 150-160: Updated auto-fill logic
   - Lines 182-189: Updated checkbox logic

2. `app/Filament/Resources/ProductInResource/Pages/CreateProductIn.php`
   - Lines 9-13: Added imports
   - Lines 18-36: Added handleRecordCreation
   - Lines 81-113: Added createProductInItems

3. `app/Filament/Resources/ProductInResource/Pages/EditProductIn.php`
   - Lines 121-129: Updated auto-fill logic
   - Lines 152-159: Updated checkbox logic
   - Lines 235-247: Updated reprocessStockBatches logic

## Memory Notes
- Purchase prices are intentionally hidden from packing admins and filled in by managers/owners during the approval process.
- For store managers and owners, the 'use default price' checkbox should be true by default on load, with the purchase price field disabled when checked, but users can uncheck it to input custom prices.
- For packing admins, if a product has a default purchase price, use it. If the default purchase price is empty/null, then set the purchase price to 0 (never leave it null).

## Result
- ✅ No more `purchase_price cannot be null` errors
- ✅ Consistent behavior across all user roles
- ✅ Proper fallback to 0 when no default price exists
- ✅ Maintained business logic and user experience
- ✅ Data integrity preserved with NOT NULL constraint
