# Dokumentasi Proses Bisnis - Approval System

## 1. Tujuan Bisnis

Approval System mengimplementasikan sistem persetujuan bertingkat untuk memastikan kontrol kualitas, akurasi data, dan akuntabilitas dalam setiap transaksi. Sistem ini mencegah kesalahan operasional dan memberikan oversight yang tepat sesuai hierarki organisasi.

## 2. Hierarki Approval

### 2.1 Struktur Approval
```
Packing Admin → Store Manager → Owner
     ↓              ↓           ↓
   Create        1st Level   Final Level
   Submit        Approval    Approval
```

### 2.2 Level Approval

#### 2.2.1 Level 1: Store Manager Approval
**Tanggung Jawab**:
- Verifikasi data operasional
- Validasi quantity dan kondisi barang
- Review foto bukti
- Koreksi harga jika diperlukan

**Authority**:
- Approve/reject transaksi dari packing admin
- Edit data sebelum forward ke owner
- Set purchase price (untuk product in)
- Review operational accuracy

#### 2.2.2 Level 2: Owner Final Approval
**Tanggung Jawab**:
- Final validation semua aspek transaksi
- Review financial impact
- Strategic decision making
- Final authorization untuk stock processing

**Authority**:
- Final approve/reject semua transaksi
- Override previous decisions
- Authorize stock movements
- Financial validation

## 3. Modul dengan Approval System

### 3.1 Product In Approval
**Workflow**:
1. **Packing Admin**: Create → WAITING_STORE_MANAGER_APPROVAL
2. **Store Manager**: Review → WAITING_OWNER_APPROVAL
3. **Owner**: Final Review → APPROVED
4. **System**: Process stock batches

**Key Points**:
- Stock batches hanya dibuat setelah owner approval
- Purchase price dapat diedit oleh store manager/owner
- Photo evidence required untuk verification

### 3.2 Product Out Approval
**Workflow**:
1. **Packing Admin**: Create + Reserve Stock → WAITING_STORE_MANAGER_APPROVAL
2. **Store Manager**: Review → WAITING_OWNER_APPROVAL  
3. **Owner**: Final Review → APPROVED
4. **System**: Deduct reserved stock

**Key Points**:
- Stock direservasi saat create (FEFO+LCOF)
- Stock dikurangi setelah final approval
- Reservasi dibatalkan jika rejected

## 4. Status Approval

### 4.1 Status Definitions
- **WAITING_STORE_MANAGER_APPROVAL**: Menunggu persetujuan kepala toko
- **WAITING_OWNER_APPROVAL**: Menunggu persetujuan owner
- **APPROVED**: Disetujui lengkap, diproses sistem
- **REJECTED**: Ditolak, tidak diproses

### 4.2 Status Transitions
```
WAITING_STORE_MANAGER_APPROVAL
    ↓ (Store Manager Approve)
WAITING_OWNER_APPROVAL
    ↓ (Owner Approve)
APPROVED

Any Status → REJECTED (dengan alasan)
```

### 4.3 Auto-Status Assignment
**Berdasarkan Role Pembuat**:
- **Packing Admin**: WAITING_STORE_MANAGER_APPROVAL
- **Store Manager**: WAITING_OWNER_APPROVAL
- **Owner**: APPROVED (langsung disetujui)

## 5. Workflow Approval Detail

### 5.1 Submission Process
1. **Data Entry**: User input semua data required
2. **Validation**: Sistem validasi business rules
3. **Photo Upload**: Upload foto bukti (jika required)
4. **Auto-Status**: Sistem set status sesuai role
5. **Notification**: Notifikasi ke approver berikutnya

### 5.2 Review Process
1. **Access Pending**: Approver akses tab pending approval
2. **Data Review**: Review semua data dan foto bukti
3. **Edit (Optional)**: Edit data jika diperlukan
4. **Decision**: Approve atau reject dengan alasan
5. **Forward/Process**: Forward ke level berikutnya atau proses final

### 5.3 Rejection Process
1. **Rejection Reason**: Input alasan penolakan yang jelas
2. **Status Update**: Status berubah ke REJECTED
3. **Notification**: Notifikasi ke pembuat record
4. **Stock Handling**: Batalkan reservasi (untuk product out)
5. **Audit Trail**: Record rejection dalam audit log

## 6. Business Rules Approval

### 6.1 Sequential Approval
- **Harus berurutan**: Tidak boleh skip level approval
- **No parallel approval**: Satu level selesai baru ke level berikutnya
- **Role-based authority**: Hanya role yang berwenang yang bisa approve

### 6.2 Edit Restrictions
- **Before approval**: Dapat edit sebelum disetujui
- **After approval**: Tidak dapat edit setelah approved
- **Ownership**: Packing admin hanya edit record sendiri
- **Status-based**: Edit berdasarkan status current

### 6.3 Stock Processing Rules
- **Product In**: Stock batches dibuat setelah owner approval
- **Product Out**: Stock dikurangi setelah owner approval
- **Reservation**: Stock direservasi saat create product out
- **Cancellation**: Reservasi dibatalkan jika rejected

## 7. Notification System

### 7.1 Approval Notifications
- **Pending approval**: Badge count di tab approval
- **New submission**: Notifikasi ke approver
- **Status change**: Notifikasi ke pembuat record
- **Rejection**: Notifikasi dengan alasan penolakan

### 7.2 Tab System
**Store Manager Tabs**:
- "Menunggu Persetujuan Kepala Toko" (badge count)
- "Semua" (untuk overview)

**Owner Tabs**:
- "Menunggu Persetujuan Owner" (badge count)
- "Semua" (untuk overview)

### 7.3 Visual Indicators
- **Status badges**: Color-coded status indicators
- **Pending counts**: Numeric badges untuk pending items
- **Priority indicators**: Untuk urgent approvals
- **Timestamp**: Waktu submission dan approval

## 8. Approval Actions

### 8.1 Approve Actions
**Store Manager Approval**:
- Button: "Setujui (Kepala Toko)"
- Confirmation modal required
- Update status dan timestamp
- Record approver information

**Owner Approval**:
- Button: "Setujui (Owner)"
- Confirmation modal required
- Final approval processing
- Trigger stock processing

### 8.2 Reject Actions
- Button: "Tolak"
- Reason input required
- Confirmation modal
- Status update ke REJECTED
- Notification ke pembuat

### 8.3 Edit Actions
- Available berdasarkan status dan role
- Form validation untuk changes
- Audit trail untuk edits
- Re-approval jika significant changes

## 9. Integration dengan Stock System

### 9.1 Product In Integration
**Pre-Approval**:
- Data validation dan photo upload
- No stock impact

**Post-Approval**:
- Stock batches creation
- Stock movement recording
- Purchase price finalization

### 9.2 Product Out Integration
**Pre-Approval**:
- Stock reservation (FEFO+LCOF)
- Availability validation
- Price calculation

**Post-Approval**:
- Stock deduction from reserved batches
- Stock movement recording
- Final price confirmation

### 9.3 Error Handling
- **Insufficient stock**: Prevent creation
- **Reservation conflicts**: Handle gracefully
- **Processing errors**: Rollback mechanism
- **Data inconsistency**: Validation checks

## 10. Audit dan Compliance

### 10.1 Approval Audit Trail
- **Submission tracking**: Siapa submit kapan
- **Approval tracking**: Siapa approve kapan
- **Edit tracking**: Perubahan apa oleh siapa
- **Rejection tracking**: Alasan rejection

### 10.2 Data Integrity
- **Approval sequence**: Memastikan sequence benar
- **Status consistency**: Status selalu konsisten
- **Stock consistency**: Stock movements akurat
- **Financial accuracy**: Perhitungan finansial benar

### 10.3 Compliance Features
- **Segregation of duties**: Pemisahan tugas yang jelas
- **Authorization levels**: Level otorisasi yang tepat
- **Documentation**: Dokumentasi lengkap
- **Audit readiness**: Siap untuk audit

## 11. Performance dan Efficiency

### 11.1 Workflow Efficiency
- **Clear responsibilities**: Tanggung jawab yang jelas
- **Minimal steps**: Langkah approval yang efisien
- **Quick actions**: Action buttons yang mudah
- **Batch processing**: Approval multiple items

### 11.2 User Experience
- **Intuitive interface**: Interface yang mudah dipahami
- **Clear status**: Status yang jelas dan visible
- **Quick navigation**: Navigasi yang cepat
- **Mobile friendly**: Dapat diakses dari mobile

### 11.3 System Performance
- **Fast loading**: Loading yang cepat
- **Real-time updates**: Update real-time
- **Efficient queries**: Query database yang efisien
- **Scalable design**: Design yang scalable

## 12. Outcome dan Manfaat

### 12.1 Untuk Quality Control
- **Error prevention**: Mencegah kesalahan operasional
- **Data accuracy**: Memastikan akurasi data
- **Process compliance**: Kepatuhan terhadap proses
- **Quality assurance**: Jaminan kualitas transaksi

### 12.2 Untuk Management Control
- **Oversight mechanism**: Mekanisme pengawasan
- **Decision control**: Kontrol pengambilan keputusan
- **Financial control**: Kontrol aspek finansial
- **Risk mitigation**: Mitigasi risiko operasional

### 12.3 Untuk Accountability
- **Clear responsibility**: Tanggung jawab yang jelas
- **Audit trail**: Jejak audit yang lengkap
- **Performance tracking**: Tracking kinerja approval
- **Compliance assurance**: Jaminan kepatuhan

### 12.4 Untuk Business Efficiency
- **Streamlined process**: Proses yang efisien
- **Reduced errors**: Pengurangan kesalahan
- **Better control**: Kontrol yang lebih baik
- **Improved accuracy**: Akurasi yang meningkat
