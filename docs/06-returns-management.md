# Dokumentasi Proses Bisnis - Returns Management

## 1. <PERSON><PERSON><PERSON>

Returns Management mengelola proses pengembalian produk dari customer dengan fleksibilitas kondisi return yang berbeda. Sistem ini memungkinkan pengembalian stok dengan kondisi yang dapat berbeda dari kondisi awal penjualan.

## 2. Role dan Permission

### 2.1 Akses Returns Management
- **Owner**: <PERSON><PERSON><PERSON> (create, read, update, delete)
- **Store Manager**: <PERSON><PERSON><PERSON> (create, read, update, delete)
- **Packing Admin**: <PERSON><PERSON><PERSON> terbatas, hanya dapat melihat dan membuat record

### 2.2 Pembatasan Data
- **Packing Admin**: Tidak dapat melihat informasi pricing dan profit
- **Store Manager & Owner**: Akses penuh termasuk data finansial

## 3. Jenis Return

### 3.1 Return Produk Single
**Karakteristik**:
- Return langsung ke stok sesuai kondisi yang dipilih
- Dapat mengubah kondisi dari kondisi asal penjualan
- Stok dikembalikan ke batch yang sesuai

### 3.2 Return Produk Paket
**Karakteristik**:
- Return per komponen individual
- Setiap komponen dapat memiliki kondisi return berbeda
- Fleksibilitas tinggi dalam handling return paket

### 3.3 Kondisi Return
- **Good**: Dikembalikan sebagai stok normal
- **Bad**: Dikembalikan sebagai stok buruk
- **Unusable**: Dikembalikan sebagai stok tidak layak pakai

## 4. Workflow Returns Management

### 4.1 Pembuatan Return Record
1. **Akses Form Create**:
   - Masuk ke menu Product Returns
   - Klik "Create Product Return"

2. **Input Data Header**:
   - Tanggal return
   - Pilih sales channel (dari mana return berasal)
   - Input catatan return
   - Input alasan return

3. **Input Return Items**:
   - Pilih produk yang di-return
   - Input quantity return
   - **Pilih kondisi return** (good/bad/unusable)
   - Sistem validasi quantity tidak melebihi yang terjual

4. **Special Handling untuk Paket**:
   - Jika return produk paket, breakdown ke komponen
   - Setiap komponen dapat dipilih kondisi berbeda
   - Quantity komponen sesuai rasio paket

### 4.2 Validasi Return
1. **Quantity Validation**:
   - Quantity return tidak boleh melebihi quantity terjual
   - Validasi berdasarkan history penjualan

2. **Condition Validation**:
   - Jika produk asal kondisi 'bad', opsi 'good' disembunyikan
   - Kondisi return tidak boleh lebih baik dari kondisi asal

3. **Business Rule Validation**:
   - Return harus dalam periode yang wajar
   - Sales channel harus sesuai dengan channel penjualan

### 4.3 Pemrosesan Return ke Stok
1. **Stock Restoration**:
   - Stok dikembalikan sesuai kondisi yang dipilih
   - Untuk kondisi 'good': tambah ke quantity
   - Untuk kondisi 'bad': tambah ke bad_quantity
   - Untuk kondisi 'unusable': tambah ke unusable_qty

2. **Batch Selection Logic**:
   - Cari batch dengan expiration_date dan purchase_price yang sama
   - Jika ada: tambahkan ke batch existing
   - Jika tidak ada: buat batch baru

3. **Stock Movement Recording**:
   - Type: "RETURN"
   - Reference: Product Return ID
   - User: User yang membuat return
   - Quantity: Sesuai dengan penambahan stok

## 5. Business Rules

### 5.1 Return Validation Rules
- **Quantity limit**: Return tidak boleh melebihi penjualan
- **Time limit**: Return dalam periode yang ditentukan (opsional)
- **Condition restriction**: Kondisi return tidak boleh lebih baik dari asal

### 5.2 Package Return Rules
- **Component breakdown**: Paket dipecah ke komponen individual
- **Individual condition**: Setiap komponen dapat kondisi berbeda
- **Quantity consistency**: Total komponen sesuai rasio paket

### 5.3 Stock Restoration Rules
- **Condition-based restoration**: Stok dikembalikan sesuai kondisi
- **Batch matching**: Cari batch yang sesuai untuk pengembalian
- **Movement tracking**: Semua return tercatat dalam stock movements

### 5.4 Condition Logic
**Allowed Transitions**:
- Good → Good ✓
- Good → Bad ✓
- Good → Unusable ✓
- Bad → Bad ✓
- Bad → Unusable ✓
- Bad → Good ✗ (tidak diperbolehkan)

## 6. Fitur Khusus

### 6.1 Flexible Condition Selection
- **Dynamic condition options**: Berdasarkan kondisi asal
- **Per-component selection**: Untuk return paket
- **Visual indicators**: Badge warna untuk kondisi

### 6.2 Return Reason Tracking
- **Reason categorization**: Defect, customer change mind, wrong item, etc.
- **Analytics support**: Data untuk analisis return patterns
- **Quality improvement**: Feedback untuk product quality

### 6.3 Sales Channel Integration
- **Channel-specific returns**: Return berdasarkan channel penjualan
- **Channel performance**: Tracking return rate per channel
- **Customer service**: Data untuk customer service improvement

## 7. Integrasi dengan Modul Lain

### 7.1 Product Out Integration
- **Return reference**: Link ke transaksi penjualan asal
- **Quantity validation**: Berdasarkan history penjualan
- **Batch tracking**: Tracking batch asal untuk return

### 7.2 Stock Management Integration
- **Stock restoration**: Mengembalikan stok sesuai kondisi
- **Batch management**: Update atau create batch untuk return
- **Movement recording**: Mencatat perpindahan stok return

### 7.3 Sales Channel Integration
- **Channel tracking**: Mencatat channel asal return
- **Performance metrics**: Data untuk analisis channel
- **Customer service**: Support untuk customer service

### 7.4 Product Management Integration
- **Package handling**: Integrasi dengan komposisi paket
- **Product validation**: Validasi produk yang di-return

## 8. Relation Manager Features

### 8.1 Return Items Display
- **Product information**: Nama produk dan details
- **Quantity tracking**: Quantity return vs quantity asal
- **Condition display**: Kondisi return dengan color coding
- **Batch information**: Info batch yang digunakan untuk return

### 8.2 Package Component Display
- **Component breakdown**: Detail komponen untuk return paket
- **Individual conditions**: Kondisi setiap komponen
- **Quantity distribution**: Pembagian quantity per komponen

### 8.3 Stock Movement History
- **Movement tracking**: History perpindahan stok dari return
- **User accountability**: Siapa yang melakukan return
- **Timestamp tracking**: Kapan return diproses

## 9. Validasi dan Kontrol

### 9.1 Business Logic Validation
- **Return quantity validation**: Tidak melebihi penjualan
- **Condition validation**: Sesuai dengan aturan kondisi
- **Time validation**: Return dalam periode yang wajar

### 9.2 Data Integrity
- **Stock consistency**: Memastikan stok konsisten setelah return
- **Batch integrity**: Batch data tetap valid setelah return
- **Movement accuracy**: Movement record akurat

### 9.3 Access Control
- **Role-based access**: Sesuai dengan permission user
- **Data visibility**: Pricing hanya untuk authorized users
- **Audit trail**: Tracking semua aktivitas return

## 10. Error Handling

### 10.1 Validation Errors
- **Quantity exceeded**: Error jika return melebihi penjualan
- **Invalid condition**: Error jika kondisi tidak valid
- **Missing data**: Error jika data required kosong

### 10.2 System Errors
- **Stock processing errors**: Handling jika gagal update stok
- **Database errors**: Graceful error handling
- **Integration errors**: Handling error integrasi dengan modul lain

## 11. Reporting dan Analytics

### 11.1 Return Analytics
- **Return rate**: Persentase return vs penjualan
- **Return reasons**: Analisis alasan return
- **Channel performance**: Return rate per channel

### 11.2 Quality Metrics
- **Product quality**: Return rate per produk
- **Condition analysis**: Analisis kondisi return
- **Improvement opportunities**: Identifikasi area perbaikan

### 11.3 Financial Impact
- **Return cost**: Biaya return untuk bisnis
- **Profit impact**: Dampak return terhadap profit
- **Channel profitability**: Profitabilitas setelah return

## 12. Outcome dan Manfaat

### 12.1 Untuk Customer Service
- **Flexible returns**: Kemudahan dalam handling return
- **Accurate tracking**: Tracking return yang akurat
- **Customer satisfaction**: Proses return yang smooth

### 12.2 Untuk Inventory Management
- **Stock accuracy**: Stok tetap akurat setelah return
- **Condition tracking**: Monitoring kondisi stok return
- **Waste reduction**: Optimalisasi handling return

### 12.3 Untuk Business Intelligence
- **Return patterns**: Analisis pola return
- **Quality insights**: Insight untuk quality improvement
- **Channel optimization**: Optimalisasi performa channel

### 12.4 Untuk Financial Control
- **Cost tracking**: Tracking biaya return
- **Profit protection**: Melindungi profit dari return
- **Financial accuracy**: Akurasi laporan keuangan
