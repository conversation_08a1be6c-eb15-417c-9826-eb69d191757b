# Dokumentasi Proses Bisnis - Product In (Penerimaan Barang)

## 1. Tujuan Bisnis

Product In mengelola proses penerimaan barang dari supplier dengan sistem approval bertingkat untuk memastikan akurasi dan akuntabilitas. Sistem ini mengintegrasikan penerimaan fisik dengan pencatatan stok dan keuangan.

## 2. Role dan Permission

### 2.1 Akses Product In
- **Owner**: <PERSON><PERSON><PERSON> penuh, dapat membuat, edit, approve, dan melihat semua record
- **Store Manager**: A<PERSON><PERSON> penuh, dapat membuat, edit, approve, dan melihat semua record
- **Packing Admin**: <PERSON>ks<PERSON> terbatas, hanya dapat membuat dan edit record sendiri

### 2.2 Pembatasan Berdasarkan Role
**Packing Admin**:
- Tidak dapat input/edit purchase price
- <PERSON>ya melihat record yang dibuat sendiri
- Tidak dapat approve record

**Store Manager & Owner**:
- Dapat input/edit purchase price
- Dapat melihat semua record
- Dapat melakukan approval

## 3. Sistem Approval Bertingkat

### 3.1 Alur Approval
```
Packing Admin → Store Manager → Owner
     ↓              ↓           ↓
   Create      Approve 1st   Approve Final
```

### 3.2 Status Approval
- **WAITING_STORE_MANAGER_APPROVAL**: Menunggu persetujuan kepala toko
- **WAITING_OWNER_APPROVAL**: Menunggu persetujuan owner
- **APPROVED**: Disetujui lengkap, stok diproses
- **REJECTED**: Ditolak, stok tidak diproses

### 3.3 Auto-Status Berdasarkan Role
**Saat Create**:
- **Packing Admin**: Status = WAITING_STORE_MANAGER_APPROVAL
- **Store Manager**: Status = WAITING_OWNER_APPROVAL
- **Owner**: Status = APPROVED (langsung disetujui)

## 4. Workflow Product In

### 4.1 Pembuatan Product In (Packing Admin)
1. **Akses Form Create**:
   - Masuk ke menu Product In
   - Klik "Create Product In"

2. **Input Data Header**:
   - Tanggal penerimaan
   - Pilih supplier dari dropdown
   - Input catatan (opsional)
   - Upload foto bukti (multiple files)

3. **Input Items**:
   - Pilih produk dari dropdown (dengan prefix notation)
   - Input quantity
   - Pilih tanggal kadaluarsa
   - Purchase price otomatis disabled (packing admin)
   - Checkbox "use default price" otomatis checked

4. **Handling Produk Paket**:
   - Jika pilih produk paket, sistem otomatis breakdown ke komponen
   - Quantity paket dibagi sesuai rasio komponen
   - Purchase price paket dibagi rata ke komponen

5. **Submit**:
   - Status otomatis: WAITING_STORE_MANAGER_APPROVAL
   - User_id diset ke packing admin
   - Redirect ke halaman view

### 4.2 Approval oleh Store Manager
1. **Akses Pending Records**:
   - Tab "Menunggu Persetujuan Kepala Toko"
   - Badge notification untuk pending count

2. **Review Data**:
   - Cek detail penerimaan
   - Verifikasi foto bukti
   - Edit purchase price jika diperlukan

3. **Approval Action**:
   - Klik "Setujui (Kepala Toko)"
   - Konfirmasi approval
   - Status berubah: WAITING_OWNER_APPROVAL
   - Timestamp dan approver tercatat

4. **Rejection (Opsional)**:
   - Klik "Tolak"
   - Input alasan penolakan
   - Status berubah: REJECTED

### 4.3 Final Approval oleh Owner
1. **Akses Pending Records**:
   - Tab "Menunggu Persetujuan Owner"
   - Badge notification untuk pending count

2. **Final Review**:
   - Cek semua detail dan approval sebelumnya
   - Verifikasi kelengkapan data

3. **Final Approval**:
   - Klik "Setujui (Owner)"
   - Konfirmasi final approval
   - Status berubah: APPROVED
   - **Stock batches dibuat otomatis**

4. **Final Rejection (Opsional)**:
   - Klik "Tolak"
   - Input alasan penolakan
   - Status berubah: REJECTED

## 5. Pemrosesan Stock Batches

### 5.1 Trigger Pemrosesan
- **Hanya setelah status = APPROVED**
- Dijalankan oleh ProductInObserver
- Otomatis saat owner approval

### 5.2 Logika Pemrosesan
**Untuk Produk Single**:
1. Cari existing batch dengan expiration_date dan purchase_price sama
2. Jika ada: tambahkan quantity ke batch existing
3. Jika tidak ada: buat batch baru

**Untuk Produk Paket**:
1. Breakdown ke komponen individual
2. Proses setiap komponen seperti produk single
3. Purchase price dibagi rata dari harga paket

### 5.3 Stock Movement Recording
- **Type**: "IN"
- **Reference**: Product In ID
- **User**: User yang membuat product in
- **Quantity**: Sesuai dengan penambahan stok

## 6. Business Rules

### 6.1 Validation Rules
- **Tanggal penerimaan tidak boleh kosong**
- **Supplier harus dipilih**
- **Minimal 1 item** dalam product in
- **Quantity harus > 0**
- **Tanggal kadaluarsa harus future date**

### 6.2 Purchase Price Rules
- **Packing admin**: Tidak dapat input, use default price
- **Store manager & owner**: Dapat input manual price
- **Default price**: Dari harga terakhir produk yang sama

### 6.3 Package Handling Rules
- **Breakdown otomatis**: Paket dipecah ke komponen
- **Price distribution**: Harga paket dibagi rata
- **Quantity calculation**: Sesuai rasio komponen

### 6.4 Approval Rules
- **Sequential approval**: Harus berurutan sesuai hierarki
- **Edit restriction**: Hanya bisa edit sebelum approval final
- **Stock processing**: Hanya setelah approval lengkap

## 7. Fitur Khusus

### 7.1 Photo Evidence
- **Multiple upload**: Dapat upload beberapa foto
- **Compression**: Otomatis dikompres untuk efisiensi storage
- **Display**: Lightbox dengan zoom capability
- **Collapsible section**: Default collapsed untuk UI yang bersih

### 7.2 Tab System
- **Semua**: Untuk user dengan full access
- **Menunggu Persetujuan Kepala Toko**: Untuk store manager
- **Menunggu Persetujuan Owner**: Untuk owner
- **Badge notification**: Menampilkan jumlah pending

### 7.3 Product Dropdown Enhancement
- **Prefix notation**: [PAKET] untuk package products
- **Search functionality**: Dapat search nama produk
- **Performance optimization**: Lazy loading untuk banyak produk

## 8. Integrasi dengan Modul Lain

### 8.1 Stock Management
- **Batch creation**: Membuat stock batches setelah approval
- **Stock movement**: Recording perpindahan stok
- **FEFO preparation**: Batch siap untuk FEFO logic

### 8.2 Supplier Management
- **Supplier tracking**: Mencatat supplier untuk setiap penerimaan
- **Performance metrics**: Data untuk evaluasi supplier

### 8.3 Product Management
- **Package breakdown**: Integrasi dengan komposisi paket
- **Price history**: Mencatat harga pembelian terbaru

### 8.4 User Management
- **Role-based workflow**: Sesuai dengan role user
- **Approval tracking**: Mencatat siapa yang approve

## 9. Validasi dan Kontrol

### 9.1 Access Control
- **Role-based access**: Sesuai dengan permission user
- **Record ownership**: Packing admin hanya akses record sendiri
- **Edit restrictions**: Berdasarkan status dan role

### 9.2 Data Validation
- **Business rule validation**: Sesuai dengan aturan bisnis
- **Referential integrity**: Validasi relasi data
- **Stock consistency**: Memastikan konsistensi stok

### 9.3 Audit Trail
- **Approval history**: Tracking semua approval
- **Edit history**: Mencatat perubahan data
- **User accountability**: Siapa melakukan apa dan kapan

## 10. Error Handling

### 10.1 Validation Errors
- **Form validation**: Real-time validation di form
- **Business rule errors**: Pesan error yang jelas
- **User guidance**: Petunjuk untuk memperbaiki error

### 10.2 System Errors
- **Stock processing errors**: Handling jika gagal buat batch
- **File upload errors**: Handling jika gagal upload foto
- **Database errors**: Graceful error handling

## 11. Outcome dan Manfaat

### 11.1 Untuk Operasional
- **Akurasi penerimaan**: Sistem approval memastikan akurasi
- **Traceability**: Jejak lengkap dari penerimaan hingga stok
- **Efficiency**: Otomatisasi pemrosesan stok

### 11.2 Untuk Manajemen
- **Control**: Kontrol penuh atas penerimaan barang
- **Accountability**: Jelas siapa yang bertanggung jawab
- **Visibility**: Real-time status penerimaan

### 11.3 Untuk Bisnis
- **Cost control**: Kontrol harga pembelian
- **Quality assurance**: Verifikasi kualitas melalui approval
- **Inventory accuracy**: Stok yang akurat dan terpercaya
