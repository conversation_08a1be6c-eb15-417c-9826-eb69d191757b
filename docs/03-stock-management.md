# Dokumentasi Proses Bisnis - Stock Management

## 1. Tujuan <PERSON>isnis

Stock Management berfungsi untuk monitoring dan pengelolaan stok produk secara real-time. Sistem ini menangani berbagai kondisi stok (good, bad, unusable) dan mengimplementasikan logika FEFO (First Expired First Out) untuk optimalisasi inventory.

## 2. Role dan Permission

### 2.1 Akses Stock Management
- **Owner**: <PERSON><PERSON><PERSON> penuh termasuk informasi harga dan nilai stok
- **Store Manager**: Aks<PERSON> penuh termasuk informasi harga dan nilai stok
- **Packing Admin**: <PERSON><PERSON><PERSON> terbatas, tidak dapat melihat harga dan nilai stok

### 2.2 Pembatasan Data
- **Pricing Information**: Hanya untuk user dengan `canViewPricing()`
- **Stock Value**: <PERSON><PERSON> ditampilkan untuk Owner dan Store Manager

## 3. Jenis Kondisi Stok

### 3.1 Stok Normal (Good Stock)
**Karakteristik**:
- Produk dalam kondisi baik dan layak jual
- Disimpan dalam kolom `quantity` di stock_batches
- Mengikuti logika FEFO untuk penjualan

### 3.2 Stok Buruk (Bad Stock)
**Karakteristik**:
- Produk rusak atau cacat tapi masih memiliki nilai
- Disimpan dalam kolom `bad_quantity` di stock_batches
- Mengikuti logika FEFO terpisah untuk bad stock
- Dapat dijual dengan harga khusus atau dikembalikan

### 3.3 Stok Tidak Layak Pakai (Unusable Stock)
**Karakteristik**:
- Produk yang tidak dapat dijual atau dikembalikan
- Disimpan dalam kolom `unusable_qty` di stock_batches
- **Tidak dihitung dalam total stok**
- **Tidak dihitung dalam nilai stok**
- Hanya untuk tracking dan reporting

## 4. Struktur Stock Batches

### 4.1 Informasi Batch
- **Product**: Relasi ke produk
- **Expiration Date**: Tanggal kadaluarsa
- **Purchase Price**: Harga pembelian per unit
- **Quantity**: Stok normal (good condition)
- **Bad Quantity**: Stok buruk (bad condition)
- **Unusable Qty**: Stok tidak layak pakai

### 4.2 Perhitungan Stok
**Total Stok Tersedia**:
```
Total Stock = Quantity + Bad_Quantity
(Unusable_qty TIDAK dihitung)
```

**Nilai Stok**:
```
Stock Value = (Quantity + Bad_Quantity) × Purchase_Price
(Unusable_qty TIDAK dihitung dalam nilai)
```

## 5. Logika FEFO (First Expired First Out)

### 5.1 FEFO untuk Stok Normal
**Prioritas Penggunaan**:
1. Batch dengan tanggal kadaluarsa paling dekat
2. Jika tanggal sama, batch dengan harga pembelian terendah (LCOF)
3. Jika harga sama, batch yang dibuat lebih dulu

### 5.2 FEFO untuk Stok Buruk
**Sistem Terpisah**:
- Bad stock memiliki logika FEFO sendiri
- Tidak tercampur dengan stok normal
- Prioritas sama: tanggal kadaluarsa → harga → waktu pembuatan

### 5.3 Implementasi dalam Product Out
- **Reservasi**: Stok direservasi saat product out dibuat
- **Deduction**: Stok dikurangi setelah approval lengkap
- **FEFO Logic**: Diterapkan saat reservasi dan deduction

## 6. Workflow Stock Management

### 6.1 Monitoring Stok Produk
1. User masuk ke menu "Stock Batches"
2. Sistem menampilkan daftar produk dengan informasi:
   - Nama produk dan brand
   - Jenis produk (single/package)
   - Total stok normal
   - Total stok buruk
   - Timeframe kadaluarsa
   - Tanggal kadaluarsa terdekat
   - Nilai stok (jika ada akses)
3. User dapat filter berdasarkan brand atau jenis produk

### 6.2 Melihat Detail Stok Produk
1. User klik "Lihat Detail Stok" pada produk
2. Sistem menampilkan:
   - Informasi dasar produk
   - Tabel batch stok dengan detail:
     - Tanggal kadaluarsa
     - Harga pembelian
     - Quantity (stok normal)
     - Bad quantity (stok buruk)
     - Unusable qty (tidak layak pakai)
   - Relation manager untuk stock movements

### 6.3 Analisis Stok Berdasarkan Kadaluarsa
**Timeframe Categories**:
- **< 1 bulan**: Perlu perhatian segera
- **1-3 bulan**: Perlu promosi atau strategi khusus
- **3-6 bulan**: Monitoring rutin
- **6-12 bulan**: Stok normal
- **> 1 tahun**: Stok aman

### 6.4 Monitoring Stok Paket
**Perhitungan Khusus**:
- Stok paket dihitung dari komponen minimum
- Formula: `MIN(komponen_stock / komponen_ratio)`
- Hanya komponen dengan kondisi 'good' yang dihitung
- Tidak ada batch fisik untuk produk paket

## 7. Business Rules

### 7.1 Stock Calculation Rules
- **Unusable stock tidak dihitung** dalam total stok
- **Unusable stock tidak dihitung** dalam nilai stok
- **Bad stock dihitung** dalam total stok dan nilai stok
- **Package stock** dihitung dari komponen minimum

### 7.2 FEFO Implementation
- **Prioritas 1**: Tanggal kadaluarsa (ascending)
- **Prioritas 2**: Harga pembelian (ascending) - LCOF
- **Prioritas 3**: Created timestamp (ascending)

### 7.3 Stock Movement Rules
- **Setiap perubahan stok** harus tercatat dalam stock_movements
- **Movement types**: IN, OUT, OPNAME, RETURN, ADJUSTMENT
- **User tracking**: Setiap movement mencatat user yang melakukan

### 7.4 Batch Management
- **Zero stock batches** tetap ditampilkan untuk tracking
- **Negative stock** tidak diperbolehkan
- **Batch consolidation** tidak dilakukan otomatis

## 8. Integrasi dengan Modul Lain

### 8.1 Product In
- **Batch creation**: Setiap product in approved membuat batch baru
- **Stock addition**: Menambah quantity sesuai kondisi
- **Price recording**: Mencatat purchase price per batch

### 8.2 Product Out
- **Stock reservation**: Menggunakan FEFO logic
- **Stock deduction**: Setelah approval lengkap
- **Movement recording**: Mencatat perpindahan stok

### 8.3 Returns Management
- **Stock restoration**: Mengembalikan stok sesuai kondisi return
- **Condition handling**: Dapat mengubah kondisi saat return
- **Batch tracking**: Mencatat asal batch untuk return

### 8.4 Stock Opname
- **Physical count**: Membandingkan stok sistem vs fisik
- **Adjustment**: Menyesuaikan stok berdasarkan hasil opname
- **Variance reporting**: Melaporkan selisih stok

## 9. Fitur Monitoring dan Reporting

### 9.1 Stock Alerts
- **Low stock warning**: Produk dengan stok < 10 unit
- **Near expiry alert**: Produk mendekati kadaluarsa
- **Zero stock notification**: Produk habis

### 9.2 Stock Value Reporting
- **Total stock value**: Nilai keseluruhan inventory
- **Value by brand**: Breakdown nilai per brand
- **Value by condition**: Breakdown nilai per kondisi stok

### 9.3 Movement History
- **Complete audit trail**: Semua perubahan stok tercatat
- **User accountability**: Tracking siapa yang melakukan perubahan
- **Transaction reference**: Link ke transaksi yang menyebabkan perubahan

## 10. Validasi dan Kontrol

### 10.1 Stock Validation
- **Non-negative stock**: Stok tidak boleh negatif
- **Batch integrity**: Setiap batch harus valid
- **Movement consistency**: Movement harus sesuai dengan perubahan stok

### 10.2 Access Control
- **Role-based visibility**: Harga hanya untuk authorized users
- **Data segregation**: User hanya melihat data yang relevan
- **Audit logging**: Semua akses tercatat

### 10.3 Data Integrity
- **Referential integrity**: Batch harus terkait dengan produk valid
- **Calculation accuracy**: Perhitungan stok harus akurat
- **Consistency check**: Regular validation untuk konsistensi data

## 11. Outcome dan Manfaat

### 11.1 Untuk Operasional
- **Real-time visibility**: Kondisi stok terkini
- **FEFO compliance**: Optimalisasi penggunaan stok
- **Condition tracking**: Monitoring berbagai kondisi stok

### 11.2 Untuk Manajemen
- **Inventory valuation**: Nilai aset inventory
- **Performance metrics**: KPI untuk inventory management
- **Decision support**: Data untuk pengambilan keputusan

### 11.3 Untuk Bisnis
- **Cost optimization**: Minimalisasi waste melalui FEFO
- **Quality control**: Tracking kondisi produk
- **Financial accuracy**: Perhitungan nilai stok yang akurat
