# Implementasi Notification untuk Sistem Produk Keluar

## Deskripsi

Dokumen ini menjelaskan perubahan yang dilakukan untuk mengubah throw exception menjadi alert notification bawaan Filament di sistem produk keluar. Perubahan ini mengikuti preferensi user untuk menggunakan notification style Filament yang konsisten dengan sistem lainnya.

## File yang Diubah

### 1. app/Services/ProductOutService.php

**Perubahan:**

-   Mempertahankan struktur exception handling yang sudah ada
-   Exception tetap di-throw dalam service untuk ditangkap oleh caller
-   Service mengembalikan array dengan format `['success' => bool, 'message' => string|null]`

**Alasan:**

-   Service layer tetap menggunakan exception untuk error handling internal
-   Caller (Pages) yang bertanggung jawab menangkap exception dan menampilkan notification

### 2. app/Services/ProductOutStockService.php

**Perubahan:**

-   Mempertahankan struktur exception handling yang sudah ada
-   Exception tetap di-throw dalam service untuk ditangkap oleh caller

**Alasan:**

-   Konsistensi dengan service layer lainnya
-   Separation of concerns: service fokus pada business logic, UI layer fokus pada user feedback

### 3. app/Filament/Resources/ProductOutResource/Pages/CreateProductOut.php

**Perubahan:**

-   Menambahkan try-catch block di method `handleRecordCreation()`
-   Menampilkan notification error menggunakan `Notification::make()->danger()`
-   Menggunakan `$this->halt()` untuk menghentikan proses

**Kode yang ditambahkan:**

```php
try {
    // ... existing code ...
    return $record;
} catch (\Exception $e) {
    // Tampilkan notifikasi error
    Notification::make()
        ->title('Gagal Menyimpan Produk Keluar')
        ->body($e->getMessage())
        ->danger()
        ->send();

    $this->halt();
}
```

### 4. app/Filament/Resources/ProductOutResource/Pages/EditProductOut.php

**Perubahan:**

-   Menambahkan try-catch block di method `afterSave()`
-   Menambahkan try-catch block di method `reprocessProductOutItems()`
-   Menampilkan notification error menggunakan `Notification::make()->danger()`
-   Menggunakan `$this->halt()` untuk menghentikan proses

**Kode yang ditambahkan:**

```php
protected function afterSave(): void
{
    try {
        // ... existing code ...
    } catch (\Exception $e) {
        // Tampilkan notifikasi error
        Notification::make()
            ->title('Gagal Memperbarui Produk Keluar')
            ->body($e->getMessage())
            ->danger()
            ->send();

        $this->halt();
    }
}
```

### 5. app/Filament/Resources/ProductOutResource/Pages/ViewProductOut.php

**Perubahan:**

-   Menambahkan try-catch block di action approval store manager
-   Menambahkan try-catch block di action approval owner
-   Menambahkan try-catch block di action rejection
-   Menampilkan notification error menggunakan `Notification::make()->danger()`

**Kode yang ditambahkan:**

```php
->action(function () use ($record, $user) {
    try {
        // ... existing approval code ...
        Notification::make()
            ->title('Produk keluar berhasil disetujui')
            ->body('Status berubah menjadi disetujui.')
            ->success()
            ->send();
    } catch (\Exception $e) {
        Notification::make()
            ->title('Gagal menyetujui produk keluar')
            ->body($e->getMessage())
            ->danger()
            ->send();
    }
})
```

### 6. app/Filament/Resources/ProductOutResource/Pages/ApprovalProductOut.php

**Perubahan:**

-   Menambahkan try-catch block di action approval store manager
-   Menambahkan try-catch block di action approval owner
-   Menambahkan try-catch block di action rejection
-   Menampilkan notification error menggunakan `Notification::make()->danger()`

## Pola Notification yang Digunakan

### Error Notification

```php
Notification::make()
    ->title('Judul Error')
    ->body($e->getMessage())
    ->danger()
    ->send();
```

### Success Notification (sudah ada)

```php
Notification::make()
    ->title('Judul Success')
    ->body('Pesan sukses')
    ->success()
    ->send();
```

### Warning Notification (sudah ada)

```php
Notification::make()
    ->title('Judul Warning')
    ->body('Pesan warning')
    ->warning()
    ->send();
```

## Keuntungan Implementasi

1. **Konsistensi UI**: Semua error message menggunakan notification style Filament yang sama
2. **User Experience**: User mendapat feedback yang jelas tanpa melihat exception page
3. **Maintainability**: Error handling terpusat di UI layer
4. **Graceful Degradation**: Sistem tidak crash ketika terjadi error, user bisa melanjutkan workflow

## Skenario Error yang Ditangani

1. **Stok tidak cukup**: Ketika quantity yang diminta melebihi stok tersedia
2. **Gagal membuat reservasi**: Ketika sistem gagal membuat reservasi stock
3. **Gagal memproses stock**: Ketika sistem gagal memproses stock movement
4. **Gagal approval**: Ketika proses approval mengalami error
5. **Gagal rejection**: Ketika proses penolakan mengalami error
6. **Gagal edit**: Ketika proses edit mengalami error

## Testing

Untuk menguji implementasi:

1. Coba buat produk keluar dengan quantity melebihi stok tersedia
2. Coba edit produk keluar yang sudah approved
3. Coba approve/reject produk keluar dalam kondisi error
4. Pastikan notification muncul dengan style yang benar
5. Pastikan proses terhenti dengan graceful (tidak crash)

## Update: Perbaikan Pesan Error dengan Nama Produk

### File yang Diubah Tambahan:

#### 7. app/Services/StockReservationService.php

**Perubahan:**

-   Mengubah pesan error dari "produk ID {$productId}" menjadi "produk {$productName}"
-   Menambahkan query untuk mendapatkan nama produk di method `createNormalStockReservation()` dan `createBadStockReservation()`
-   Mengubah format error collection untuk menyertakan nama produk
-   Mengubah format pesan error utama dari JSON menjadi format yang lebih user-friendly

**Kode yang diubah:**

```php
// Sebelum
throw new \Exception("Stock normal tidak cukup untuk produk ID {$productId}. Tersedia: {$totalAvailable}, dibutuhkan: {$requiredQuantity}");

// Sesudah
$product = \App\Models\Product::find($productId);
$productName = $product ? $product->name : "ID {$productId}";
throw new \Exception("Stock normal tidak cukup untuk produk {$productName}. Tersedia: {$totalAvailable}, dibutuhkan: {$requiredQuantity}");
```

**Format error collection:**

```php
// Sebelum
$errors[] = [
    'item_id' => $item->id,
    'error' => $e->getMessage()
];

// Sesudah
$errors[] = [
    'product_name' => $productName,
    'error' => $e->getMessage()
];
```

#### 8. app/Services/ProductOutStockService.php

**Perubahan:**

-   Mengubah format pesan error dari JSON menjadi format yang lebih readable
-   Menampilkan nama produk dalam pesan error

**Kode yang diubah:**

```php
// Sebelum
throw new \Exception("Gagal membuat reservasi stock: " . json_encode($result['errors']));

// Sesudah
$errorMessages = [];
foreach ($result['errors'] as $error) {
    if (isset($error['product_name'])) {
        $errorMessages[] = "{$error['product_name']}: {$error['error']}";
    } else {
        $errorMessages[] = $error['error'];
    }
}
throw new \Exception("Gagal membuat reservasi stock:\n" . implode("\n", $errorMessages));
```

### Contoh Pesan Error Sebelum dan Sesudah:

**Sebelum:**

```
Gagal membuat reservasi untuk beberapa item: [{"item_id":5,"error":"Stock normal tidak cukup untuk produk ID 1. Tersedia: 75, dibutuhkan: 100"}]
```

**Sesudah:**

```
Gagal membuat reservasi untuk beberapa item:
Indomie Goreng: Stock normal tidak cukup untuk produk Indomie Goreng. Tersedia: 75, dibutuhkan: 100
```

## Catatan

-   Exception masih tetap di-throw di service layer untuk logging dan debugging
-   UI layer bertanggung jawab menangkap exception dan menampilkan notification
-   Menggunakan `$this->halt()` untuk menghentikan proses form submission
-   Notification menggunakan plain text tanpa markdown sesuai preferensi user
-   Pesan error sekarang menampilkan nama produk yang lebih user-friendly
-   Format error message menggunakan newline untuk readability yang lebih baik
