# Dokumentasi Proses Bisnis - Opname Management

## 1. <PERSON><PERSON><PERSON>nis

Opname Management mengelola proses stock opname (physical inventory count) untuk memastikan akurasi data stok sistem dengan kondisi fisik di gudang. Sistem ini membantu mengidentifikasi dan mengoreksi perbedaan stok untuk menjaga integritas inventory.

## 2. Role dan Permission

### 2.1 Akses Opname Management
- **Owner**: <PERSON><PERSON><PERSON> penuh (create, read, update, delete)
- **Store Manager**: <PERSON><PERSON><PERSON> penuh (create, read, update, delete)
- **Packing Admin**: <PERSON><PERSON><PERSON> terbatas (create, read), tidak dapat delete

### 2.2 Pembatasan Data
- **Packing Admin**: Tidak dapat melihat informasi nilai stok dan financial impact
- **Store Manager & Owner**: Aks<PERSON> penuh termasuk data finansial

## 3. Jenis Stock Opname

### 3.1 Full Opname
**Karakteristik**:
- Menghitung semua produk di gudang
- Dilakukan secara berkala (bulanan/quarterly)
- Memerlukan waktu dan resources yang significant

### 3.2 Partial Opname
**Karakteristik**:
- Menghitung produk tertentu saja
- Dilakukan untuk produk dengan discrepancy tinggi
- Lebih efisien untuk targeted checking

### 3.3 Cycle Count
**Karakteristik**:
- Menghitung produk secara bergilir
- Dilakukan secara regular untuk maintenance
- Membantu menjaga akurasi stok berkelanjutan

## 4. Workflow Opname Management

### 4.1 Persiapan Opname
1. **Planning Phase**:
   - Tentukan scope opname (full/partial)
   - Tentukan tanggal dan waktu opname
   - Assign petugas opname

2. **Pre-Opname Setup**:
   - Freeze stock movements (opsional)
   - Print stock list untuk reference
   - Prepare counting sheets

### 4.2 Pelaksanaan Opname
1. **Akses Form Create**:
   - Masuk ke menu Stock Opname
   - Klik "Create Stock Opname"

2. **Input Data Header**:
   - Tanggal opname
   - Input catatan opname
   - Pilih petugas yang melakukan

3. **Input Opname Items**:
   - Pilih produk yang diopname
   - Pilih batch specific (expiration date + purchase price)
   - **System Quantity**: Ditampilkan otomatis dari sistem
   - **Actual Quantity**: Input hasil counting fisik
   - **Variance**: Dihitung otomatis (Actual - System)

4. **Batch-Level Counting**:
   - Opname dilakukan per batch (expiration + price)
   - Memungkinkan identifikasi discrepancy yang spesifik
   - Lebih akurat untuk tracking

### 4.3 Pemrosesan Hasil Opname
1. **Variance Analysis**:
   - Sistem menghitung selisih per batch
   - Positive variance: Actual > System (kelebihan stok)
   - Negative variance: Actual < System (kekurangan stok)

2. **Stock Adjustment**:
   - Update stock batches sesuai actual quantity
   - Untuk positive variance: tambah stok
   - Untuk negative variance: kurangi stok

3. **Stock Movement Recording**:
   - Type: "OPNAME"
   - Reference: Stock Opname ID
   - User: Petugas opname
   - Quantity: Sesuai dengan adjustment

## 5. Business Rules

### 5.1 Opname Validation Rules
- **Tanggal opname tidak boleh future date**
- **Actual quantity tidak boleh negatif**
- **Minimal 1 item** dalam opname
- **Batch harus valid** dan existing

### 5.2 Adjustment Rules
- **Automatic adjustment**: Stok disesuaikan otomatis setelah save
- **Movement recording**: Semua adjustment tercatat
- **Audit trail**: Tracking siapa melakukan opname

### 5.3 Variance Tolerance
- **Acceptable variance**: Toleransi selisih yang wajar
- **Investigation threshold**: Variance besar perlu investigasi
- **Approval requirement**: Variance besar perlu approval

### 5.4 Batch-Level Rules
- **Batch specificity**: Opname per batch untuk akurasi
- **Expiration tracking**: Mempertahankan tracking kadaluarsa
- **Price consistency**: Mempertahankan purchase price

## 6. Fitur Khusus

### 6.1 System vs Actual Comparison
- **Real-time variance calculation**: Hitung selisih otomatis
- **Visual indicators**: Color coding untuk variance
- **Percentage variance**: Persentase selisih untuk analysis

### 6.2 Batch Selection
- **Dropdown batch**: Pilih batch berdasarkan expiration + price
- **Current stock display**: Tampilkan stok sistem saat ini
- **Batch information**: Detail batch untuk reference

### 6.3 Variance Reporting
- **Variance summary**: Total variance per opname
- **Item-level variance**: Variance per item/batch
- **Financial impact**: Nilai finansial dari variance

## 7. Integrasi dengan Modul Lain

### 7.1 Stock Management Integration
- **Stock adjustment**: Update stock batches langsung
- **Movement recording**: Mencatat perpindahan stok
- **Batch integrity**: Mempertahankan integritas batch

### 7.2 Product Management Integration
- **Product validation**: Validasi produk yang diopname
- **Package handling**: Opname komponen paket individual
- **Product information**: Display info produk untuk reference

### 7.3 User Management Integration
- **User tracking**: Mencatat siapa melakukan opname
- **Role-based access**: Akses sesuai role user
- **Accountability**: Audit trail untuk accountability

### 7.4 Reporting Integration
- **Variance reports**: Data untuk variance analysis
- **Accuracy metrics**: KPI untuk inventory accuracy
- **Trend analysis**: Tracking accuracy over time

## 8. Relation Manager Features

### 8.1 Opname Items Display
- **Product information**: Nama produk dan batch details
- **Quantity comparison**: System vs Actual vs Variance
- **Percentage variance**: Persentase selisih
- **Financial impact**: Nilai finansial variance

### 8.2 Stock Movement History
- **Movement tracking**: History adjustment dari opname
- **User accountability**: Siapa yang melakukan adjustment
- **Timestamp tracking**: Kapan adjustment dilakukan

### 8.3 Variance Analysis
- **Variance breakdown**: Detail variance per item
- **Trend analysis**: Pattern variance over time
- **Root cause**: Identifikasi penyebab variance

## 9. Validasi dan Kontrol

### 9.1 Data Validation
- **Quantity validation**: Actual quantity harus valid
- **Batch validation**: Batch harus existing dan valid
- **Date validation**: Tanggal opname harus reasonable

### 9.2 Business Logic Validation
- **Variance threshold**: Validasi variance yang wajar
- **Approval workflow**: Variance besar perlu approval
- **Consistency check**: Memastikan konsistensi data

### 9.3 Access Control
- **Role-based access**: Sesuai dengan permission user
- **Data segregation**: User hanya akses data yang relevan
- **Audit logging**: Tracking semua aktivitas opname

## 10. Error Handling

### 10.1 Validation Errors
- **Invalid quantity**: Error jika quantity tidak valid
- **Missing batch**: Error jika batch tidak ditemukan
- **Date errors**: Error jika tanggal tidak valid

### 10.2 System Errors
- **Adjustment errors**: Handling jika gagal adjust stok
- **Database errors**: Graceful error handling
- **Calculation errors**: Validasi perhitungan variance

## 11. Reporting dan Analytics

### 11.1 Opname Reports
- **Opname summary**: Ringkasan hasil opname
- **Variance report**: Detail variance per opname
- **Accuracy metrics**: KPI inventory accuracy

### 11.2 Trend Analysis
- **Accuracy trends**: Trend akurasi over time
- **Variance patterns**: Pattern variance untuk improvement
- **Product analysis**: Produk dengan variance tinggi

### 11.3 Financial Impact
- **Variance value**: Nilai finansial variance
- **Cost of inaccuracy**: Biaya ketidakakuratan stok
- **ROI of opname**: Return on investment opname program

## 12. Best Practices

### 12.1 Opname Frequency
- **High-value items**: Opname lebih sering
- **Fast-moving items**: Cycle count regular
- **Slow-moving items**: Opname periodic

### 12.2 Accuracy Improvement
- **Root cause analysis**: Analisis penyebab variance
- **Process improvement**: Perbaikan proses berdasarkan findings
- **Training**: Training untuk improve accuracy

### 12.3 Technology Integration
- **Barcode scanning**: Untuk improve accuracy
- **Mobile devices**: Untuk efficiency opname
- **Real-time updates**: Update sistem real-time

## 13. Outcome dan Manfaat

### 13.1 Untuk Inventory Accuracy
- **Data integrity**: Memastikan akurasi data stok
- **Variance identification**: Identifikasi discrepancy
- **Continuous improvement**: Perbaikan berkelanjutan

### 13.2 Untuk Financial Control
- **Asset accuracy**: Akurasi nilai aset inventory
- **Loss prevention**: Identifikasi dan prevent loss
- **Cost control**: Kontrol biaya inventory

### 13.3 Untuk Operational Excellence
- **Process improvement**: Perbaikan proses operasional
- **Accountability**: Meningkatkan accountability
- **Performance metrics**: KPI untuk performance

### 13.4 Untuk Compliance
- **Audit readiness**: Siap untuk audit eksternal
- **Regulatory compliance**: Memenuhi requirement regulasi
- **Documentation**: Dokumentasi lengkap untuk compliance
