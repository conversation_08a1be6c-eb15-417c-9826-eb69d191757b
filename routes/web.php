<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\StockPdfController;
use App\Http\Controllers\TransactionReportController;

Route::get('/', function () {
    return view('welcome');
});

// Route untuk test database connection
Route::get('/test-db', function () {
    try {
        \Illuminate\Support\Facades\DB::connection()->getPdo();
        return response()->json([
            'status' => 'success',
            'message' => 'Database connection successful',
            'database' => config('database.default')
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Database connection failed: ' . $e->getMessage()
        ], 500);
    }
});

// Rute untuk Download PDF Stok - Hanya untuk user yang terautentikasi
Route::middleware('auth')->group(function () {
    Route::get('/stock/pdf/total', [StockPdfController::class, 'downloadTotalStock'])->name('stock.pdf.total');
    Route::get('/stock/pdf/batch', [StockPdfController::class, 'downloadBatchStock'])->name('stock.pdf.batch');

    // Rute untuk Export Excel Laporan Transaksi - Hanya untuk store manager dan owner
    Route::get('/transaction-report/export/product-in', [TransactionReportController::class, 'exportProductIn'])->name('transaction-report.export-product-in');
    Route::get('/transaction-report/export/product-out', [TransactionReportController::class, 'exportProductOut'])->name('transaction-report.export-product-out');
    Route::get('/transaction-report/export/product-return', [TransactionReportController::class, 'exportProductReturn'])->name('transaction-report.export-product-return');
    Route::get('/transaction-report/export/stock-opname', [TransactionReportController::class, 'exportStockOpname'])->name('transaction-report.export-stock-opname');
});
