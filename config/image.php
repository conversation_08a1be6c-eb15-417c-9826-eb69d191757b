<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Image Compression Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for image compression using Intervention Image
    |
    */

    'compression' => [
        /*
        |--------------------------------------------------------------------------
        | JPEG Quality
        |--------------------------------------------------------------------------
        |
        | Quality level for JPEG compression (1-100)
        | 80 = Good balance between quality and file size
        | 90 = High quality, larger file size
        | 70 = Lower quality, smaller file size
        |
        */
        'quality' => env('IMAGE_COMPRESSION_QUALITY', 80),

        /*
        |--------------------------------------------------------------------------
        | Maximum Dimensions
        |--------------------------------------------------------------------------
        |
        | Maximum width and height for resized images
        | Images larger than these dimensions will be resized while preserving
        | aspect ratio. Smaller images will not be upscaled.
        |
        */
        'max_width' => env('IMAGE_COMPRESSION_MAX_WIDTH', 1920),
        'max_height' => env('IMAGE_COMPRESSION_MAX_HEIGHT', 1080),

        /*
        |--------------------------------------------------------------------------
        | Enable Compression
        |--------------------------------------------------------------------------
        |
        | Enable or disable image compression globally
        |
        */
        'enabled' => env('IMAGE_COMPRESSION_ENABLED', true),

        /*
        |--------------------------------------------------------------------------
        | Supported Formats
        |--------------------------------------------------------------------------
        |
        | Image formats that will be processed for compression
        | All formats will be converted to JPEG
        |
        */
        'supported_formats' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],

        /*
        |--------------------------------------------------------------------------
        | Notification Settings
        |--------------------------------------------------------------------------
        |
        | Settings for compression notifications
        |
        */
        'notifications' => [
            'show_success' => env('IMAGE_COMPRESSION_SHOW_SUCCESS', false),
            'show_failure' => env('IMAGE_COMPRESSION_SHOW_FAILURE', true),
        ],
    ],
];
