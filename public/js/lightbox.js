// Lightbox functionality
document.addEventListener("DOMContentLoaded", function () {
    initLightbox();
});

function initLightbox() {
    // Create lightbox overlay if it doesn't exist
    let lightboxOverlay = document.getElementById("lightbox-overlay");
    if (!lightboxOverlay) {
        lightboxOverlay = document.createElement("div");
        lightboxOverlay.id = "lightbox-overlay";
        lightboxOverlay.className = "lightbox-overlay";
        lightboxOverlay.innerHTML = `
            <div class="lightbox-content">
                <button class="lightbox-close" type="button">&times;</button>
                <img class="lightbox-image" src="" alt="Foto Bukti">
            </div>
        `;
        document.body.appendChild(lightboxOverlay);
    }

    const lightboxImage = lightboxOverlay.querySelector(".lightbox-image");
    const lightboxClose = lightboxOverlay.querySelector(".lightbox-close");

    // Add click event to all images in lightbox galleries
    function attachLightboxEvents() {
        const galleries = document.querySelectorAll(".lightbox-image-gallery");
        galleries.forEach((gallery) => {
            const images = gallery.querySelectorAll("img");
            images.forEach((img) => {
                // Remove existing event listeners to prevent duplicates
                img.removeEventListener("click", handleImageClick);
                img.addEventListener("click", handleImageClick);
            });
        });
    }

    function handleImageClick(e) {
        e.preventDefault();
        const imgSrc = e.target.src;
        if (imgSrc) {
            lightboxImage.src = imgSrc;
            lightboxOverlay.classList.add("active");
            document.body.style.overflow = "hidden";
        }
    }

    // Close lightbox
    function closeLightbox() {
        lightboxOverlay.classList.remove("active");
        document.body.style.overflow = "";
        lightboxImage.src = "";
    }

    // Event listeners for closing
    lightboxClose.addEventListener("click", closeLightbox);
    lightboxOverlay.addEventListener("click", function (e) {
        if (e.target === lightboxOverlay) {
            closeLightbox();
        }
    });

    // Close with ESC key
    document.addEventListener("keydown", function (e) {
        if (
            e.key === "Escape" &&
            lightboxOverlay.classList.contains("active")
        ) {
            closeLightbox();
        }
    });

    // Initial attachment
    attachLightboxEvents();

    // Re-attach events when Filament updates the DOM (for dynamic content)
    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (
                mutation.type === "childList" &&
                mutation.addedNodes.length > 0
            ) {
                // Check if any added nodes contain lightbox galleries
                mutation.addedNodes.forEach(function (node) {
                    if (node.nodeType === 1) {
                        // Element node
                        if (
                            node.classList &&
                            node.classList.contains("lightbox-image-gallery")
                        ) {
                            attachLightboxEvents();
                        } else if (
                            node.querySelector &&
                            node.querySelector(".lightbox-image-gallery")
                        ) {
                            attachLightboxEvents();
                        }
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true,
    });
}

document.addEventListener("livewire:navigated", function () {
    initLightbox();
});
document.addEventListener("livewire:load", function () {
    initLightbox();
});
