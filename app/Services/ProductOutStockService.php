<?php

namespace App\Services;

use App\Models\ProductOut;
use App\Models\ProductOutStatus;
use App\Models\Product;
use App\Services\StockReservationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductOutStockService
{
    protected ProductOutService $productOutService;
    protected PackageStockService $packageStockService;
    protected StockReservationService $reservationService;

    public function __construct()
    {
        $this->productOutService = app(ProductOutService::class);
        $this->packageStockService = new PackageStockService(
            new StockBatchService(),
            new BadStockService()
        );
        $this->reservationService = new StockReservationService(new BadStockService());
    }

    /**
     * Buat reservasi stock untuk ProductOut yang baru dibuat
     */
    public function createReservationsForNewProductOut(ProductOut $productOut): void
    {
        try {
            $result = $this->reservationService->createReservationsForProductOut($productOut);

            if (!$result['success']) {
                Log::error("Failed to create reservations for ProductOut", [
                    'product_out_id' => $productOut->id,
                    'errors' => $result['errors']
                ]);

                // Format error message dengan nama produk
                $errorMessages = [];
                foreach ($result['errors'] as $error) {
                    if (isset($error['product_name'])) {
                        $errorMessages[] = "{$error['product_name']}: {$error['error']}";
                    } else {
                        $errorMessages[] = $error['error'];
                    }
                }

                throw new \Exception("Gagal membuat reservasi stock:\n" . implode("\n", $errorMessages));
            }

            Log::info("Created reservations for ProductOut", [
                'product_out_id' => $productOut->id,
                'reservations_count' => count($result['results'])
            ]);
        } catch (\Exception $e) {
            Log::error("Exception creating reservations for ProductOut", [
                'product_out_id' => $productOut->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Update reservasi stock untuk ProductOut yang diedit
     */
    public function updateReservationsForEditedProductOut(ProductOut $productOut): void
    {
        // Hanya update reservasi jika belum approved
        if ($productOut->isApproved()) {
            Log::info("Skipping reservation update for approved ProductOut", [
                'product_out_id' => $productOut->id,
                'status' => $productOut->status->value
            ]);
            return;
        }

        $this->createReservationsForNewProductOut($productOut);
    }

    /**
     * Proses stock untuk produk keluar yang sudah disetujui
     */
    public function processApprovedProductOut(ProductOut $productOut, bool $useTransaction = true): void
    {
        if (!$productOut->isApproved()) {
            Log::warning("Attempting to process stock for non-approved ProductOut", [
                'product_out_id' => $productOut->id,
                'status' => $productOut->status->value
            ]);
            return;
        }

        $processStock = function () use ($productOut) {
            // Cek apakah sudah ada reservasi yang perlu dikonversi
            $hasReservations = $productOut->stockReservations()->exists();

            if ($hasReservations) {
                // Jika ada reservasi, konversi ke stock movements
                Log::info("Converting reservations to stock movements for ProductOut", [
                    'product_out_id' => $productOut->id
                ]);

                $this->reservationService->convertReservationsToStockMovements($productOut);
            } else {
                // Jika tidak ada reservasi (owner langsung approve), proses stock normal
                Log::info("Processing stock directly for owner-approved ProductOut", [
                    'product_out_id' => $productOut->id
                ]);

                $this->processStockDirectly($productOut);
            }

            Log::info("Stock processed for approved ProductOut", [
                'product_out_id' => $productOut->id,
                'has_reservations' => $hasReservations
            ]);
        };

        if ($useTransaction) {
            DB::transaction($processStock);
        } else {
            $processStock();
        }
    }

    /**
     * Proses stock langsung tanpa reservasi (untuk owner yang langsung approve)
     */
    protected function processStockDirectly(ProductOut $productOut): void
    {
        // Pisahkan items berdasarkan jenis
        $normalItems = [];
        $badItems = [];
        $packageItems = [];

        foreach ($productOut->items as $item) {
            // Rekonstruksi data item untuk service
            $productId = $this->getProductIdFromItem($item);

            if (!$productId) {
                Log::warning("Cannot determine product_id for ProductOutItem", [
                    'item_id' => $item->id,
                    'product_out_id' => $productOut->id
                ]);
                continue;
            }

            $itemData = [
                'product_id' => $productId,
                'quantity' => $item->package_quantity ?? $item->quantity,
                'sale_price' => $item->sale_price,
                'final_price' => $item->final_price,
                'discount_id' => $item->discount_id,
                'is_manual_discount' => $item->is_manual_discount,
            ];

            if ($item->stock_condition === 'bad') {
                $badItems[] = $itemData;
            } elseif ($item->package_product_id) {
                $packageItems[] = $itemData;
            } else {
                $normalItems[] = $itemData;
            }
        }

        $results = [];

        // Process normal stock items
        if (!empty($normalItems)) {
            $results[] = $this->productOutService->processProductOut($productOut, $normalItems);
        }

        // Process bad stock items
        if (!empty($badItems)) {
            $results[] = $this->productOutService->processBadStockOut($productOut, $badItems);
        }

        // Process package items
        if (!empty($packageItems)) {
            $results[] = $this->packageStockService->processPackageStockOut($productOut, $packageItems);

            // Hapus item paket original setelah diproses (sama seperti ProductIn)
            // Untuk package items, product_id di database adalah null dan package_product_id berisi ID paket
            $packageProductIds = array_column($packageItems, 'product_id');
            $productOut->items()
                ->whereIn('package_product_id', $packageProductIds)
                ->whereNull('batch_id') // Hanya hapus yang belum diproses
                ->delete();
        }

        // Check if any processing failed
        foreach ($results as $result) {
            if ($result['success'] === false) {
                throw new \Exception($result['message']);
            }
        }

        Log::info("Stock processed directly for ProductOut", [
            'product_out_id' => $productOut->id,
            'normal_items_count' => count($normalItems),
            'bad_items_count' => count($badItems),
            'package_items_count' => count($packageItems)
        ]);
    }

    /**
     * Proses stock untuk produk keluar yang baru dibuat
     */
    public function processNewProductOut(ProductOut $productOut, bool $useTransaction = false): void
    {
        if ($productOut->isApproved()) {
            // Jika langsung approved (owner yang membuat), langsung proses stock
            // Tidak menggunakan transaction karena sudah dalam transaction dari CreateProductOut
            $this->processApprovedProductOut($productOut, $useTransaction);
        } else {
            // Jika belum approved, buat reservasi
            // Tidak perlu transaction karena sudah dalam transaction dari CreateProductOut
            $this->createReservationsForNewProductOut($productOut);
        }
    }

    /**
     * Kembalikan stock untuk produk keluar yang ditolak
     */
    public function restoreStockForRejectedProductOut(ProductOut $productOut, bool $useTransaction = true): void
    {
        // Untuk reprocess, tidak perlu cek isRejected karena kita ingin restore stock yang sudah diproses
        $isForReprocess = !$useTransaction;

        if (!$isForReprocess && !$productOut->isRejected()) {
            Log::warning("Attempting to restore stock for non-rejected ProductOut", [
                'product_out_id' => $productOut->id,
                'status' => $productOut->status->value
            ]);
            return;
        }

        $restoreStock = function () use ($productOut) {
            $hasReservations = $this->hasActiveReservations($productOut);
            $hasProcessedStock = $this->hasProcessedStock($productOut);

            Log::info("Restoring stock for rejected ProductOut", [
                'product_out_id' => $productOut->id,
                'has_reservations' => $hasReservations,
                'has_processed_stock' => $hasProcessedStock
            ]);

            // Hapus reservasi jika ada (ini akan otomatis mengembalikan stok yang direservasi)
            if ($hasReservations) {
                $this->reservationService->clearReservationsForProductOut($productOut);
                Log::info("Cleared reservations for rejected ProductOut", [
                    'product_out_id' => $productOut->id
                ]);
            }

            // Jika ada stock movements yang sudah diproses, kembalikan stock
            if ($hasProcessedStock) {
                foreach ($productOut->items as $item) {
                    if ($item->batch_id) {
                        // Kembalikan stock ke batch yang bersangkutan
                        $batch = $item->stockBatch;
                        if ($batch) {
                            if ($item->stock_condition === 'bad') {
                                $batch->increment('bad_quantity', $item->quantity);
                            } else {
                                $batch->increment('quantity', $item->quantity);
                            }

                            // Catat stock movement untuk pengembalian
                            $batch->stockMovements()->create([
                                'type' => 'IN',
                                'quantity' => $item->quantity,
                                'description' => "RESTORE from rejected OUT #{$productOut->id}",
                                'user_id' => auth()->id(),
                                'movement_date' => now(),
                            ]);

                            Log::info("Restored stock to batch", [
                                'product_out_id' => $productOut->id,
                                'batch_id' => $batch->id,
                                'quantity' => $item->quantity,
                                'stock_condition' => $item->stock_condition
                            ]);
                        }
                    }
                }
            }

            Log::info("Stock restoration completed for rejected ProductOut", [
                'product_out_id' => $productOut->id
            ]);
        };

        if ($useTransaction) {
            DB::transaction($restoreStock);
        } else {
            $restoreStock();
        }
    }

    /**
     * Cek apakah produk keluar sudah memiliki stock movements
     */
    public function hasProcessedStock(ProductOut $productOut): bool
    {
        return $productOut->items()->whereNotNull('batch_id')->exists();
    }

    /**
     * Cek apakah produk keluar memiliki reservasi aktif
     */
    public function hasActiveReservations(ProductOut $productOut): bool
    {
        return $productOut->stockReservations()->exists();
    }

    /**
     * Cek apakah produk keluar memiliki stock yang perlu dikembalikan (reservasi atau stock movements)
     */
    public function hasStockToRestore(ProductOut $productOut): bool
    {
        return $this->hasProcessedStock($productOut) || $this->hasActiveReservations($productOut);
    }

    /**
     * Reprocess stock untuk produk keluar yang diedit (hanya jika sudah approved)
     */
    public function reprocessStockForEditedProductOut(ProductOut $productOut, bool $useTransaction = true): void
    {
        if (!$productOut->isApproved()) {
            // Jika belum approved, tidak perlu reprocess stock
            Log::info("Skipping stock reprocessing for non-approved ProductOut", [
                'product_out_id' => $productOut->id,
                'status' => $productOut->status->value
            ]);
            return;
        }

        $reprocessStock = function () use ($productOut) {
            Log::info("Reprocessing stock for edited ProductOut", [
                'product_out_id' => $productOut->id
            ]);

            // Kembalikan stock yang sudah diproses sebelumnya
            $this->restoreStockForRejectedProductOut($productOut, false);

            // Proses ulang stock dengan data baru
            $this->processApprovedProductOut($productOut, false);
        };

        if ($useTransaction) {
            DB::transaction($reprocessStock);
        } else {
            $reprocessStock();
        }
    }

    /**
     * Mendapatkan product_id dari item (untuk package atau single product)
     */
    protected function getProductIdFromItem($item): ?int
    {
        // Untuk package products
        if ($item->package_product_id) {
            return $item->package_product_id;
        }

        // Untuk single products - gunakan product_id langsung
        if ($item->product_id) {
            return $item->product_id;
        }

        // Fallback: untuk single products yang sudah memiliki batch
        if ($item->batch_id && $item->stockBatch) {
            return $item->stockBatch->product_id;
        }

        // Fallback: cari dari relasi product (jika ada)
        if ($item->product) {
            return $item->product->id;
        }

        return null;
    }
}
