<?php

namespace App\Services;

use App\Models\StockBatch;
use App\Models\StockReservation;
use App\Models\ProductOut;
use App\Models\ProductOutItem;
use App\Models\Product;
use App\Services\BadStockService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockReservationService
{
    protected BadStockService $badStockService;

    public function __construct(BadStockService $badStockService)
    {
        $this->badStockService = $badStockService;
    }

    /**
     * Buat reservasi stock untuk ProductOut berdasarkan FEFO dan LCOF
     */
    public function createReservationsForProductOut(ProductOut $productOut): array
    {
        $results = [];
        $errors = [];

        DB::transaction(function () use ($productOut, &$results, &$errors) {
            // Hapus reservasi lama jika ada (untuk edit case)
            $this->clearReservationsForProductOut($productOut);

            foreach ($productOut->items as $item) {
                try {
                    $result = $this->createReservationForItem($item);
                    $results[] = $result;
                } catch (\Exception $e) {
                    // Get product name for better error reporting
                    $productName = 'Unknown Product';
                    if ($item->product_id) {
                        $product = \App\Models\Product::find($item->product_id);
                        $productName = $product ? $product->name : "ID {$item->product_id}";
                    } elseif ($item->package_product_id) {
                        $product = \App\Models\Product::find($item->package_product_id);
                        $productName = $product ? "[PAKET] {$product->name}" : "Paket ID {$item->package_product_id}";
                    }

                    $errors[] = [
                        'product_name' => $productName,
                        'error' => $e->getMessage()
                    ];
                    Log::error("Failed to create reservation for ProductOutItem", [
                        'item_id' => $item->id,
                        'product_out_id' => $productOut->id,
                        'product_name' => $productName,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if (!empty($errors)) {
                $errorMessages = [];
                foreach ($errors as $error) {
                    $errorMessages[] = "{$error['product_name']}: {$error['error']}";
                }
                throw new \Exception("Gagal membuat reservasi untuk beberapa item:\n" . implode("\n", $errorMessages));
            }
        });

        return [
            'success' => empty($errors),
            'results' => $results,
            'errors' => $errors
        ];
    }

    /**
     * Buat reservasi untuk single ProductOutItem
     */
    public function createReservationForItem(ProductOutItem $item): array
    {
        // Skip jika item dari package (akan dihandle oleh PackageStockService)
        if ($item->isFromPackage()) {
            return $this->createReservationForPackageComponent($item);
        }

        $stockCondition = $item->stock_condition ?? 'good';
        $isNormalStock = $stockCondition !== 'bad';

        if ($isNormalStock) {
            return $this->createNormalStockReservation($item);
        } else {
            return $this->createBadStockReservation($item);
        }
    }

    /**
     * Buat reservasi untuk normal stock menggunakan FEFO dan LCOF
     */
    protected function createNormalStockReservation(ProductOutItem $item): array
    {
        $productId = $item->product_id;
        $requiredQuantity = $item->quantity;

        // Get product name for better error message
        $product = \App\Models\Product::find($productId);
        $productName = $product ? $product->name : "ID {$productId}";

        // Get available batches dengan FEFO dan LCOF, exclude yang sudah direservasi
        $availableBatches = $this->getAvailableNormalStockBatches($productId);

        $totalAvailable = $availableBatches->sum(function ($batch) {
            return $batch->quantity - $this->getReservedQuantity($batch->id, 'normal');
        });

        if ($totalAvailable < $requiredQuantity) {
            throw new \Exception("Stock normal tidak cukup untuk produk {$productName}. Tersedia: {$totalAvailable}, dibutuhkan: {$requiredQuantity}");
        }

        $reservations = [];
        $remainingQuantity = $requiredQuantity;

        foreach ($availableBatches as $batch) {
            if ($remainingQuantity <= 0) break;

            $availableInBatch = $batch->quantity - $this->getReservedQuantity($batch->id, 'normal');
            if ($availableInBatch <= 0) continue;

            $quantityToReserve = min($availableInBatch, $remainingQuantity);

            $reservation = StockReservation::create([
                'product_out_id' => $item->product_out_id,
                'product_out_item_id' => $item->id,
                'batch_id' => $batch->id,
                'quantity' => $quantityToReserve,
                'stock_condition' => 'normal',
                'reserved_at' => now()
            ]);

            $reservations[] = $reservation;
            $remainingQuantity -= $quantityToReserve;
        }

        return [
            'item_id' => $item->id,
            'reservations' => $reservations,
            'total_reserved' => $requiredQuantity - $remainingQuantity
        ];
    }

    /**
     * Buat reservasi untuk bad stock menggunakan FEFO
     */
    protected function createBadStockReservation(ProductOutItem $item): array
    {
        $productId = $item->product_id;
        $requiredQuantity = $item->quantity;

        // Get product name for better error message
        $product = \App\Models\Product::find($productId);
        $productName = $product ? $product->name : "ID {$productId}";

        // Get available bad stock batches dengan FEFO
        $availableBatches = $this->getAvailableBadStockBatches($productId);

        $totalAvailable = $availableBatches->sum(function ($batch) {
            return $batch->bad_quantity - $this->getReservedQuantity($batch->id, 'bad');
        });

        if ($totalAvailable < $requiredQuantity) {
            throw new \Exception("Stock buruk tidak cukup untuk produk {$productName}. Tersedia: {$totalAvailable}, dibutuhkan: {$requiredQuantity}");
        }

        $reservations = [];
        $remainingQuantity = $requiredQuantity;

        foreach ($availableBatches as $batch) {
            if ($remainingQuantity <= 0) break;

            $availableInBatch = $batch->bad_quantity - $this->getReservedQuantity($batch->id, 'bad');
            if ($availableInBatch <= 0) continue;

            $quantityToReserve = min($availableInBatch, $remainingQuantity);

            $reservation = StockReservation::create([
                'product_out_id' => $item->product_out_id,
                'product_out_item_id' => $item->id,
                'batch_id' => $batch->id,
                'quantity' => $quantityToReserve,
                'stock_condition' => 'bad',
                'reserved_at' => now()
            ]);

            $reservations[] = $reservation;
            $remainingQuantity -= $quantityToReserve;
        }

        return [
            'item_id' => $item->id,
            'reservations' => $reservations,
            'total_reserved' => $requiredQuantity - $remainingQuantity
        ];
    }

    /**
     * Buat reservasi untuk komponen package
     */
    protected function createReservationForPackageComponent(ProductOutItem $item): array
    {
        $packageProduct = $item->packageProduct;
        if (!$packageProduct || !$packageProduct->isPackage()) {
            throw new \Exception("Invalid package product for item ID {$item->id}");
        }

        // Load relasi jika belum dimuat
        if (!$packageProduct->relationLoaded('packageCompositions')) {
            $packageProduct->load('packageCompositions.componentProduct');
        }

        $packageQuantity = $item->package_quantity;
        $reservations = [];

        // Buat reservasi untuk setiap komponen dalam package
        foreach ($packageProduct->packageCompositions as $composition) {
            $componentProduct = $composition->componentProduct;
            $requiredQuantity = $composition->quantity * $packageQuantity;

            // Skip jika component tidak ada atau quantity invalid
            if (!$componentProduct || $requiredQuantity <= 0) {
                continue;
            }

            // Tentukan stock condition berdasarkan composition
            $stockCondition = $composition->stock_condition === 'bad' ? 'bad' : 'normal';

            // Buat reservasi untuk komponen ini
            $componentReservations = $this->createComponentReservation(
                $item,
                $componentProduct,
                $requiredQuantity,
                $stockCondition
            );

            $reservations = array_merge($reservations, $componentReservations);
        }

        return [
            'item_id' => $item->id,
            'reservations' => $reservations,
            'total_reserved' => count($reservations),
            'note' => "Package component reservations for {$packageProduct->name}"
        ];
    }

    /**
     * Buat reservasi untuk single component
     */
    protected function createComponentReservation(ProductOutItem $item, Product $component, int $requiredQuantity, string $stockCondition): array
    {
        // Validasi component
        if (!$component || !$component->id) {
            throw new \Exception("Invalid component product");
        }

        // Get available batches untuk komponen berdasarkan stock condition
        if ($stockCondition === 'bad') {
            $availableBatches = $this->getAvailableBadStockBatches($component->id);
            $stockField = 'bad_quantity';
        } else {
            $availableBatches = $this->getAvailableNormalStockBatches($component->id);
            $stockField = 'quantity';
        }

        $totalAvailable = $availableBatches->sum(function ($batch) use ($stockCondition) {
            if ($stockCondition === 'bad') {
                return $batch->bad_quantity - $this->getReservedQuantity($batch->id, 'bad');
            } else {
                return $batch->quantity - $this->getReservedQuantity($batch->id, 'normal');
            }
        });

        if ($totalAvailable < $requiredQuantity) {
            $conditionLabel = $stockCondition === 'bad' ? 'buruk' : 'baik';
            throw new \Exception("Stock {$conditionLabel} tidak cukup untuk komponen {$component->name}. Tersedia: {$totalAvailable}, dibutuhkan: {$requiredQuantity}");
        }

        $reservations = [];
        $remainingQuantity = $requiredQuantity;

        foreach ($availableBatches as $batch) {
            if ($remainingQuantity <= 0) break;

            if ($stockCondition === 'bad') {
                $availableInBatch = $batch->bad_quantity - $this->getReservedQuantity($batch->id, 'bad');
            } else {
                $availableInBatch = $batch->quantity - $this->getReservedQuantity($batch->id, 'normal');
            }

            if ($availableInBatch <= 0) continue;

            $quantityToReserve = min($availableInBatch, $remainingQuantity);

            $reservation = StockReservation::create([
                'product_out_id' => $item->product_out_id,
                'product_out_item_id' => $item->id,
                'batch_id' => $batch->id,
                'quantity' => $quantityToReserve,
                'stock_condition' => $stockCondition,
                'reserved_at' => now()
            ]);

            $reservations[] = $reservation;
            $remainingQuantity -= $quantityToReserve;
        }

        return $reservations;
    }

    /**
     * Get available normal stock batches dengan FEFO dan LCOF
     */
    protected function getAvailableNormalStockBatches(int $productId)
    {
        return StockBatch::where('product_id', $productId)
            ->where('quantity', '>', 0)
            ->orderBy('expiration_date', 'asc')
            ->orderBy('purchase_price', 'asc')
            ->get();
    }

    /**
     * Get available bad stock batches dengan FEFO
     */
    protected function getAvailableBadStockBatches(int $productId)
    {
        return StockBatch::where('product_id', $productId)
            ->where('bad_quantity', '>', 0)
            ->orderBy('expiration_date', 'asc')
            ->orderBy('purchase_price', 'asc')
            ->get();
    }

    /**
     * Get jumlah yang sudah direservasi untuk batch tertentu
     */
    public function getReservedQuantity(int $batchId, string $stockCondition = 'normal'): int
    {
        return StockReservation::active()
            ->byBatch($batchId)
            ->byStockCondition($stockCondition)
            ->sum('quantity');
    }

    /**
     * Hapus semua reservasi untuk ProductOut
     */
    public function clearReservationsForProductOut(ProductOut $productOut): void
    {
        StockReservation::where('product_out_id', $productOut->id)->delete();

        Log::info("Cleared reservations for ProductOut", [
            'product_out_id' => $productOut->id
        ]);
    }

    /**
     * Convert reservasi menjadi actual stock movement (saat approved)
     */
    public function convertReservationsToStockMovements(ProductOut $productOut): void
    {
        $reservations = StockReservation::where('product_out_id', $productOut->id)
            ->with(['stockBatch', 'productOutItem'])
            ->get();

        if ($reservations->isEmpty()) {
            Log::info("No reservations found to convert for ProductOut", [
                'product_out_id' => $productOut->id
            ]);
            return;
        }

        // Group reservasi berdasarkan original ProductOutItem untuk membuat item baru per batch
        $originalItems = [];
        $newItemsCreated = 0;

        foreach ($reservations as $reservation) {
            $batch = $reservation->stockBatch;
            $originalItem = $reservation->productOutItem;

            if (!$batch || !$originalItem) {
                Log::warning("Invalid reservation found", [
                    'reservation_id' => $reservation->id,
                    'batch_exists' => !is_null($batch),
                    'item_exists' => !is_null($originalItem)
                ]);
                continue;
            }

            // Kurangi stock dari batch
            if ($reservation->stock_condition === 'normal') {
                $batch->decrement('quantity', $reservation->quantity);
            } else {
                $batch->decrement('bad_quantity', $reservation->quantity);
            }

            // Catat stock movement
            $description = "OUT #{$productOut->id} at {$productOut->date_out->format('Y-m-d')} to {$productOut->salesChannel->name}";
            if ($reservation->stock_condition === 'bad') {
                $description .= " (bad stock)";
            }

            $batch->stockMovements()->create([
                'type' => 'OUT',
                'quantity' => -$reservation->quantity,
                'quantity_after' => $reservation->stock_condition === 'normal' ? $batch->quantity : $batch->bad_quantity,
                'description' => $description,
                'user_id' => $productOut->user_id,
                'movement_date' => $productOut->date_out->format('Y-m-d'),
            ]);

            // Simpan data original item untuk membuat item baru per batch
            if (!isset($originalItems[$originalItem->id])) {
                $originalItems[$originalItem->id] = [
                    'original_item' => $originalItem,
                    'reservations' => []
                ];
            }
            $originalItems[$originalItem->id]['reservations'][] = $reservation;
        }

        // Buat ProductOutItem baru untuk setiap batch reservation
        foreach ($originalItems as $originalItemId => $data) {
            $originalItem = $data['original_item'];
            $itemReservations = $data['reservations'];

            // Hapus original item (akan diganti dengan item per batch)
            $originalItem->delete();

            // Buat item baru untuk setiap reservation
            foreach ($itemReservations as $reservation) {
                $newItem = new ProductOutItem();
                $newItem->product_out_id = $originalItem->product_out_id;
                $newItem->product_id = $originalItem->product_id;
                $newItem->batch_id = $reservation->batch_id;
                $newItem->quantity = $reservation->quantity;

                // Hitung harga untuk komponen paket
                if ($originalItem->isFromPackage()) {
                    $packageProduct = $originalItem->packageProduct;
                    if ($packageProduct) {
                        $componentCount = $packageProduct->packageCompositions()->count();
                        if ($componentCount > 0) {
                            // Bagi harga jual dan harga akhir dengan jumlah komponen
                            $newItem->sale_price = ($originalItem->sale_price ?? 0) / $componentCount;
                            $newItem->final_price = ($originalItem->final_price ?? $originalItem->sale_price ?? 0) / $componentCount;
                        } else {
                            $newItem->sale_price = 0;
                            $newItem->final_price = 0;
                        }
                    } else {
                        $newItem->sale_price = 0;
                        $newItem->final_price = 0;
                    }
                } else {
                    // Untuk produk satuan, gunakan harga asli
                    $newItem->sale_price = $originalItem->sale_price;
                    $newItem->final_price = $originalItem->final_price;
                }

                $newItem->discount_id = $originalItem->discount_id;
                $newItem->is_manual_discount = $originalItem->is_manual_discount;
                $newItem->package_product_id = $originalItem->package_product_id;
                $newItem->package_quantity = $originalItem->package_quantity;
                $newItem->package_group_id = $originalItem->package_group_id;
                $newItem->stock_condition = $reservation->stock_condition === 'normal' ? 'good' : 'bad';
                $newItem->save();

                $newItemsCreated++;
            }
        }

        // Hapus reservasi setelah dikonversi
        StockReservation::where('product_out_id', $productOut->id)->delete();

        Log::info("Converted reservations to stock movements with separate items per batch", [
            'product_out_id' => $productOut->id,
            'reservations_count' => $reservations->count(),
            'original_items_count' => count($originalItems),
            'new_items_created' => $newItemsCreated
        ]);
    }

    /**
     * Get available stock untuk produk (exclude reserved)
     */
    public function getAvailableStock(int $productId, string $stockCondition = 'normal'): int
    {
        if ($stockCondition === 'normal') {
            $totalStock = StockBatch::where('product_id', $productId)->sum('quantity');
            $reservedStock = StockReservation::active()
                ->byStockCondition('normal')
                ->whereHas('stockBatch', function ($query) use ($productId) {
                    $query->where('product_id', $productId);
                })
                ->sum('quantity');
        } else {
            $totalStock = StockBatch::where('product_id', $productId)->sum('bad_quantity');
            $reservedStock = StockReservation::active()
                ->byStockCondition('bad')
                ->whereHas('stockBatch', function ($query) use ($productId) {
                    $query->where('product_id', $productId);
                })
                ->sum('quantity');
        }

        return max(0, $totalStock - $reservedStock);
    }
}
