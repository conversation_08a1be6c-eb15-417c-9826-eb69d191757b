<?php

namespace App\Services;

use App\Models\StockBatch;
use App\Models\ProductOut;
use App\Models\ProductOutItem;
use App\Models\Product;
use App\Services\BadStockService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductOutService
{
  protected $stockBatchService;

  public function __construct(StockBatchService $stockBatchService)
  {
    $this->stockBatchService = $stockBatchService;
  }

  /**
   * Proses produk keluar berdasarkan FEFO (First Expired First Out)
   * dan LCOF (Low Cost Out First).
   *
   * @param ProductOut $productOut
   * @param array $items Data item dari form repeater (product_id, quantity, sale_price, final_price, discount_id, is_manual_discount)
   * @return array Returns ['success' => bool, 'message' => string|null]
   *
   * Note: Jika batch_id tidak disediakan, sistem akan otomatis memilih batch
   * berdasarkan FEFO (First Expired First Out) dan LCOF (Low Cost Out First)
   */
  public function processProductOut(ProductOut $productOut, array $items): array
  {
    try {
      DB::transaction(function () use ($productOut, $items) {
        foreach ($items as $item) {
          $productId = $item['product_id'];
          $quantityToDeduct = $item['quantity'];
          $product = Product::find($productId);
          $productName = $product ? $product->name : 'ID ' . $productId;

          if ($quantityToDeduct <= 0) {
            continue; // Lewati jika kuantitas tidak valid
          }

          // 1. Ambil semua batch stok yang tersedia untuk produk ini, urutkan FEFO/LCOF
          $availableBatches = StockBatch::where('product_id', $productId)
            ->where('quantity', '>', 0)
            ->orderBy('expiration_date', 'asc')
            ->orderBy('purchase_price', 'asc')
            ->get();

          // 2. Validasi total stok yang tersedia
          $totalAvailableStock = $availableBatches->sum('quantity');
          if ($totalAvailableStock < $quantityToDeduct) {
            throw new \Exception("Stok tidak cukup untuk produk {$productName}. Stok tersedia: {$totalAvailableStock}, dibutuhkan: {$quantityToDeduct}");
          }

          // 3. Iterasi melalui batch dan kurangi stok
          $remainingQuantityToDeduct = $quantityToDeduct;
          foreach ($availableBatches as $batch) {
            if ($remainingQuantityToDeduct <= 0) {
              break; // Kuantitas sudah terpenuhi
            }

            $quantityFromThisBatch = min($remainingQuantityToDeduct, $batch->quantity);

            // Kuantitas sebelum dikurangi (untuk referensi)
            // $quantityBefore = $batch->quantity;

            // Kurangi stok batch
            $batch->quantity -= $quantityFromThisBatch;
            $batch->save();

            // Simpan kuantitas setelah dikurangi untuk logging
            $quantityAfter = $batch->quantity;

            // 4. Update ProductOutItem yang sudah ada dengan batch_id
            // Cari item yang sesuai dengan product_id dan belum memiliki batch_id
            $existingItem = $productOut->items()
                ->where('product_id', $productId)
                ->whereNull('batch_id')
                ->where('stock_condition', 'good')
                ->first();

            if ($existingItem) {
                $existingItem->batch_id = $batch->id;
                $existingItem->save();
            } else {
                // Fallback: buat item baru jika tidak ditemukan
                $outItem = new ProductOutItem();
                $outItem->product_out_id = $productOut->id;
                $outItem->batch_id = $batch->id;
                $outItem->quantity = $quantityFromThisBatch;
                $outItem->sale_price = $item['sale_price'];
                $outItem->final_price = !empty($item['final_price']) ? $item['final_price'] : null;
                $outItem->discount_id = !empty($item['discount_id']) ? $item['discount_id'] : null;
                $outItem->is_manual_discount = isset($item['is_manual_discount']) ? (bool)$item['is_manual_discount'] : false;
                $outItem->stock_condition = 'good';
                $outItem->save();
            }

            // 5. Catat pergerakan stok
            $description = "OUT #{$productOut->id} at {$productOut->date_out->format('Y-m-d')} to {$productOut->salesChannel->name}";
            $this->stockBatchService->recordStockMovement(
              $batch->id,
              $productOut->user_id,
              -$quantityFromThisBatch, // Kuantitas keluar (negatif)
              $quantityAfter,         // Kuantitas setelah keluar
              'OUT',
              $description,
              $productOut->date_out // Gunakan date_out dari ProductOut
            );

            // Kurangi sisa kuantitas yang perlu diambil
            $remainingQuantityToDeduct -= $quantityFromThisBatch;
          }

          // Pastikan semua kuantitas berhasil dikurangi (sebagai sanity check)
          if ($remainingQuantityToDeduct > 0) {
            // Ini seharusnya tidak terjadi jika validasi di awal benar
            Log::error("Gagal mengurangi stok sepenuhnya untuk produk {$productName} pada ProductOut ID {$productOut->id}. Sisa: {$remainingQuantityToDeduct}");
            throw new \Exception("Terjadi kesalahan sistem saat mengurangi stok untuk produk {$productName}.");
          }
        }
      });
      return ['success' => true, 'message' => null];
    } catch (\Exception $e) {
      return ['success' => false, 'message' => $e->getMessage()];
    }
  }

  /**
   * Process bad stock out using FEFO logic
   *
   * @param ProductOut $productOut
   * @param array $items Data item dari form repeater (product_id, quantity, sale_price, final_price, discount_id, is_manual_discount)
   * @return array Returns ['success' => bool, 'message' => string|null]
   */
  public function processBadStockOut(ProductOut $productOut, array $items): array
  {
    try {
      DB::transaction(function () use ($productOut, $items) {
        $badStockService = new BadStockService();

        foreach ($items as $item) {
          $productId = $item['product_id'];
          $quantityToDeduct = $item['quantity'];
          $product = Product::find($productId);
          $productName = $product ? $product->name : 'ID ' . $productId;

          if ($quantityToDeduct <= 0) {
            continue;
          }

          // Get bad stock batches using FEFO
          $result = $badStockService->getBadStockBatchesForOut($productId, $quantityToDeduct);

          if ($result['shortage'] > 0) {
            throw new \Exception("Stok buruk tidak cukup untuk produk {$productName}. Tersedia: {$result['total_available']}, dibutuhkan: {$quantityToDeduct}");
          }

          // Process each batch
          foreach ($result['batches'] as $batchData) {
            $batch = $batchData['batch'];
            $quantityFromThisBatch = $batchData['quantity'];

            // Reduce bad stock
            $badStockService->reduceBadStock(
              $batch->id,
              $quantityFromThisBatch,
              $productOut->user_id,
              "BAD STOCK OUT #{$productOut->id} at {$productOut->date_out->format('Y-m-d')} to {$productOut->salesChannel->name}",
              $productOut->date_out->format('Y-m-d')
            );

            // Update ProductOutItem yang sudah ada dengan batch_id
            // Cari item yang sesuai dengan product_id dan belum memiliki batch_id
            $existingItem = $productOut->items()
                ->where('product_id', $productId)
                ->whereNull('batch_id')
                ->where('stock_condition', 'bad')
                ->first();

            if ($existingItem) {
                $existingItem->batch_id = $batch->id;
                $existingItem->save();
            } else {
                // Fallback: buat item baru jika tidak ditemukan
                $outItem = new ProductOutItem();
                $outItem->product_out_id = $productOut->id;
                $outItem->batch_id = $batch->id;
                $outItem->quantity = $quantityFromThisBatch;
                $outItem->sale_price = $item['sale_price'];
                $outItem->final_price = !empty($item['final_price']) ? $item['final_price'] : null;
                $outItem->discount_id = !empty($item['discount_id']) ? $item['discount_id'] : null;
                $outItem->is_manual_discount = isset($item['is_manual_discount']) ? (bool)$item['is_manual_discount'] : false;
                $outItem->stock_condition = 'bad';
                $outItem->save();
            }
          }
        }
      });
      return ['success' => true, 'message' => null];
    } catch (\Exception $e) {
      return ['success' => false, 'message' => $e->getMessage()];
    }
  }

  /**
   * Dapatkan batch stok produk berdasarkan FEFO dan harga terendah
   *
   * @param int $productId
   * @param int $requiredQuantity
   * @return array
   */
  public function getStockBatchesForProduct(int $productId, int $requiredQuantity = 1): array
  {
    // Ambil batch dengan tanggal kedaluwarsa terdekat dan harga terendah
    // yang memiliki stok mencukupi
    $batches = StockBatch::where('product_id', $productId)
      ->where('quantity', '>=', $requiredQuantity)
      ->orderBy('expiration_date', 'asc')
      ->orderBy('purchase_price', 'asc')
      ->get();

    return $batches->toArray();
  }

  /**
   * Get bad stock batches for product
   *
   * @param int $productId
   * @param int $requiredQuantity
   * @return array
   */
  public function getBadStockBatchesForProduct(int $productId, int $requiredQuantity = 1): array
  {
    $badStockService = new BadStockService();
    $batches = $badStockService->getAvailableBadStockBatches($productId);

    return $batches->toArray();
  }
}
