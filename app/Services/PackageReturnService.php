<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductReturn;
use App\Models\StockBatch;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PackageReturnService
{
    protected BadStockService $badStockService;
    protected UnusableStockService $unusableStockService;

    public function __construct()
    {
        $this->badStockService = new BadStockService();
        $this->unusableStockService = new UnusableStockService();
    }

    /**
     * Validasi ketersediaan batch untuk retur produk paket
     */
    public function validatePackageReturnAvailability(int $packageProductId, int $quantity, array $componentExpirationDates): array
    {
        $package = Product::find($packageProductId);

        if (!$package || !$package->isPackage()) {
            return [
                'valid' => false,
                'message' => 'Produk bukan merupakan produk paket.',
                'details' => []
            ];
        }

        $compositions = $package->packageCompositions()->with('component')->get();
        $validationDetails = [];
        $isValid = true;

        foreach ($compositions as $composition) {
            $componentId = $composition->component_product_id;
            $requiredQuantity = $composition->quantity * $quantity;

            // Gunakan unique key: component_id + stock_condition
            $uniqueKey = $componentId . '_' . $composition->stock_condition;
            $expirationDate = $componentExpirationDates[$uniqueKey] ?? null;

            if (!$expirationDate) {
                $validationDetails[] = [
                    'component_name' => $composition->component->name,
                    'condition' => $composition->stock_condition,
                    'required' => $requiredQuantity,
                    'available' => 0,
                    'sufficient' => false,
                    'error' => 'Tanggal kedaluarsa tidak dipilih'
                ];
                $isValid = false;
                continue;
            }

            // Cek ketersediaan batch dengan tanggal kedaluarsa yang sesuai
            // Untuk retur, yang penting adalah batch dengan tanggal tersebut ada
            // Tidak peduli kondisi stok saat ini karena retur bisa menambah kondisi baru
            $availableBatch = $this->findAvailableBatch(
                $componentId,
                $expirationDate,
                null // Tidak filter berdasarkan kondisi
            );

            // Untuk retur, yang penting adalah batch dengan tanggal tersebut ada

            $componentDetail = [
                'component_name' => $composition->component->name,
                'condition' => $composition->stock_condition,
                'required' => $requiredQuantity,
                'batch_exists' => $availableBatch !== null,
                'expiration_date' => $expirationDate,
                'batch_id' => $availableBatch?->id
            ];

            $validationDetails[] = $componentDetail;

            // Untuk retur, yang penting adalah batch dengan tanggal tersebut ada
            if (!$availableBatch) {
                $isValid = false;
            }
        }

        // Log detail validasi untuk debugging
        if (!$isValid) {
            \Log::error('Package validation failed', [
                'package_name' => $package->name,
                'package_id' => $packageProductId,
                'quantity' => $quantity,
                'validation_details' => $validationDetails,
                'component_expiration_dates' => $componentExpirationDates
            ]);
        }

        $message = $isValid ? 'Semua komponen tersedia untuk retur.' : 'Batch tidak ditemukan untuk beberapa komponen dengan tanggal kedaluarsa yang dipilih.';

        return [
            'valid' => $isValid,
            'message' => $message,
            'details' => $validationDetails
        ];
    }

    /**
     * Memproses retur produk paket
     */
    public function processPackageReturn(ProductReturn $productReturn, array $packageItems): void
    {
        DB::transaction(function () use ($productReturn, $packageItems) {
            foreach ($packageItems as $item) {
                $packageProduct = Product::find($item['product_id']);

                if (!$packageProduct || !$packageProduct->isPackage()) {
                    continue;
                }

                $packageQuantity = $item['quantity'];
                $componentExpirationDates = $item['component_expiration_dates'] ?? [];
                $componentConditions = $item['component_conditions'] ?? [];

                // Validasi ketersediaan terlebih dahulu
                $validation = $this->validatePackageReturnAvailability(
                    $packageProduct->id,
                    $packageQuantity,
                    $componentExpirationDates
                );

                if (!$validation['valid']) {
                    throw new Exception(
                        "Retur paket {$packageProduct->name} gagal: " . $validation['message']
                    );
                }

                // Proses breakdown untuk setiap komponen
                $this->processPackageComponents(
                    $productReturn,
                    $packageProduct,
                    $packageQuantity,
                    $componentExpirationDates,
                    $componentConditions
                );
            }
        });
    }

    /**
     * Memproses komponen-komponen dari produk paket
     */
    private function processPackageComponents(
        ProductReturn $productReturn,
        Product $package,
        int $packageQuantity,
        array $componentExpirationDates,
        array $componentConditions
    ): void {
        $compositions = $package->packageCompositions()->with('component')->get();
        $packageGroupId = Str::uuid()->toString();

        foreach ($compositions as $composition) {
            $componentId = $composition->component_product_id;
            $componentQuantity = $composition->quantity * $packageQuantity;
            $originalCondition = $composition->stock_condition;

            // Gunakan unique key: component_id + stock_condition
            $uniqueKey = $componentId . '_' . $composition->stock_condition;
            $expirationDate = $componentExpirationDates[$uniqueKey];
            $userSelectedCondition = $componentConditions[$uniqueKey] ?? 'good';

            // Tentukan kondisi stok final berdasarkan logic
            $finalCondition = $this->determineFinalStockCondition($userSelectedCondition, $originalCondition);

            // Buat ProductReturnItem untuk komponen
            $returnItem = $productReturn->items()->create([
                'product_id' => $componentId,
                'expiration_date' => $expirationDate,
                'quantity' => $componentQuantity,
                'condition' => $userSelectedCondition, // Kondisi yang dipilih user per komponen
                'package_product_id' => $package->id,
                'package_group_id' => $packageGroupId,
                'original_stock_condition' => $originalCondition
            ]);

            // Proses stok berdasarkan kondisi final
            $this->processComponentStock($returnItem, $finalCondition, $productReturn);
        }
    }

    /**
     * Menentukan kondisi stok final berdasarkan logic bisnis
     */
    private function determineFinalStockCondition(string $userSelectedCondition, string $originalCondition): string
    {
        // Karena UI sudah mencegah user memilih "good" untuk komponen "bad",
        // kondisi final selalu sama dengan pilihan user
        return $userSelectedCondition;
    }

    /**
     * Memproses stok komponen berdasarkan kondisi final
     */
    private function processComponentStock(
        $returnItem,
        string $finalCondition,
        ProductReturn $productReturn
    ): void {
        $batch = $this->findAvailableBatch(
            $returnItem->product_id,
            $returnItem->expiration_date,
            $returnItem->original_stock_condition
        );

        if (!$batch) {
            throw new Exception(
                "Batch tidak ditemukan untuk komponen '{$returnItem->product->name}' " .
                "dengan tanggal kadaluarsa {$returnItem->expiration_date}"
            );
        }

        if ($finalCondition === 'good') {
            $this->processGoodStockReturn($returnItem, $batch, $productReturn);
        } elseif ($finalCondition === 'bad') {
            $this->processBadStockReturn($returnItem, $batch, $productReturn);
        } elseif ($finalCondition === 'unusable') {
            $this->processUnusableStockReturn($returnItem, $batch, $productReturn);
        }
    }

    /**
     * Mencari batch yang tersedia untuk komponen
     */
    private function findAvailableBatch(int $productId, string $expirationDate, ?string $condition = null): ?StockBatch
    {
        return StockBatch::where('product_id', $productId)
            ->where('expiration_date', $expirationDate)
            ->orderBy('purchase_price', 'asc')
            ->first();
    }

    /**
     * Mendapatkan quantity batch berdasarkan kondisi
     */
    private function getBatchQuantityByCondition(StockBatch $batch, string $condition): int
    {
        return match ($condition) {
            'good' => $batch->quantity,
            'bad' => $batch->bad_quantity,
            default => 0
        };
    }

    /**
     * Memproses retur ke stok normal
     */
    private function processGoodStockReturn($returnItem, StockBatch $batch, ProductReturn $productReturn): void
    {
        $batch->quantity += $returnItem->quantity;
        $batch->save();

        // Record stock movement
        \App\Models\StockMovement::create([
            'batch_id' => $batch->id,
            'user_id' => $productReturn->user_id,
            'quantity' => $returnItem->quantity,
            'quantity_after' => $batch->quantity,
            'type' => 'RETURN',
            'description' => "PACKAGE RETURN #{$productReturn->id} - {$returnItem->packageProduct->name} (komponen: {$returnItem->product->name})",
            'movement_date' => $productReturn->date_ret,
        ]);

        $returnItem->batch_id = $batch->id;
        $returnItem->save();
    }

    /**
     * Memproses retur ke bad stock
     */
    private function processBadStockReturn($returnItem, StockBatch $batch, ProductReturn $productReturn): void
    {
        $this->badStockService->addBadStock(
            $returnItem->product_id,
            $returnItem->expiration_date,
            null, // Purchase price null untuk legacy behavior
            $returnItem->quantity,
            $productReturn->user_id,
            "PACKAGE BAD RETURN #{$productReturn->id} - {$returnItem->packageProduct->name} (komponen: {$returnItem->product->name})",
            $productReturn->date_ret->format('Y-m-d')
        );

        $returnItem->batch_id = $batch->id;
        $returnItem->save();
    }

    /**
     * Memproses retur ke unusable stock
     */
    private function processUnusableStockReturn($returnItem, StockBatch $batch, ProductReturn $productReturn): void
    {
        $this->unusableStockService->addUnusableStock(
            $returnItem->product_id,
            $returnItem->expiration_date,
            $returnItem->quantity,
            $productReturn->user_id,
            "PACKAGE UNUSABLE RETURN #{$productReturn->id} - {$returnItem->packageProduct->name} (komponen: {$returnItem->product->name})",
            $productReturn->date_ret->format('Y-m-d')
        );

        $returnItem->batch_id = $batch->id;
        $returnItem->save();
    }

    /**
     * Mendapatkan informasi komponen paket untuk form
     */
    public function getPackageComponentsInfo(int $packageProductId): array
    {
        $package = Product::find($packageProductId);

        if (!$package || !$package->isPackage()) {
            return [];
        }

        $compositions = $package->packageCompositions()->with('component')->get();
        $componentsInfo = [];

        foreach ($compositions as $composition) {
            $component = $composition->component;

            // Dapatkan batch yang tersedia untuk komponen ini
            $availableBatches = StockBatch::where('product_id', $component->id)
                ->where(function ($query) use ($composition) {
                    if ($composition->stock_condition === 'good') {
                        $query->where('quantity', '>', 0);
                    } else {
                        $query->where('bad_quantity', '>', 0);
                    }
                })
                ->orderBy('expiration_date', 'asc')
                ->get();

            $componentsInfo[] = [
                'component_id' => $component->id,
                'component_name' => $component->name,
                'quantity_per_package' => $composition->quantity,
                'stock_condition' => $composition->stock_condition,
                'stock_condition_label' => $composition->stock_condition === 'good' ? 'Baik' : 'Buruk',
                'available_batches' => $availableBatches->map(function ($batch) use ($composition) {
                    return [
                        'expiration_date' => $batch->expiration_date->format('Y-m-d'),
                        'available_quantity' => $this->getBatchQuantityByCondition($batch, $composition->stock_condition),
                        'purchase_price' => $batch->purchase_price
                    ];
                })->toArray()
            ];
        }

        return $componentsInfo;
    }
}
