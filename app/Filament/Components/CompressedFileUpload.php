<?php

namespace App\Filament\Components;

use Filament\Forms\Components\FileUpload;
use App\Services\ImageCompressionService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class CompressedFileUpload
{
    /**
     * Create a new compressed file upload component
     */
    public static function make(string $name): FileUpload
    {
        $component = FileUpload::make($name);

        // Set up the afterStateUpdated callback for compression
        $component->afterStateUpdated(function ($state, $component) {
            if (!config('image.compression.enabled', true)) {
                return;
            }

            static::compressUploadedFiles($state, $component);
        });

        return $component;
    }

    /**
     * Configure for evidence photos with optimal settings
     */
    public static function evidencePhotos(string $name): FileUpload
    {
        return static::make($name)
            ->label('Foto Bukti')
            ->image()
            ->multiple()
            ->maxFiles(5)
            ->disk('public')
            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'])
            ->maxSize(10240) // 10MB max before compression
            ->imagePreviewHeight('150')
            ->panelLayout('compact')
            ->removeUploadedFileButtonPosition('top-right')
            ->uploadButtonPosition('left')
            ->uploadProgressIndicatorPosition('right')
            ->columnSpanFull();
    }

    /**
     * Compress uploaded files
     */
    protected static function compressUploadedFiles($state, $component): void
    {
        if (!$state || !is_array($state)) {
            return;
        }

        $disk = $component->getDiskName();
        $directory = $component->getDirectory();
        $compressedFiles = [];
        $hasFailures = false;
        $successCount = 0;
        $compressionService = app(ImageCompressionService::class);

        foreach ($state as $index => $file) {
            if ($file instanceof TemporaryUploadedFile) {
                // For temporary files, we need to handle them differently
                static::compressTemporaryFile($file, $disk, $directory);
            } elseif (is_string($file)) {
                // For already stored files, compress them
                $filePath = $directory ? $directory . '/' . $file : $file;
                $result = $compressionService->compressImage($filePath, $disk);

                if ($result['success']) {
                    $successCount++;
                    if ($result['compressed']) {
                        // Update the file path if it was renamed (e.g., PNG to JPG)
                        if (isset($result['new_file_path'])) {
                            $newFileName = basename($result['new_file_path']);
                            $compressedFiles[$index] = $newFileName;
                        }
                    }
                } else {
                    $hasFailures = true;
                }
            }
        }

        // Update state with new file names if any were changed
        if (!empty($compressedFiles)) {
            $newState = $state;
            foreach ($compressedFiles as $index => $newFileName) {
                $newState[$index] = $newFileName;
            }
            $component->state($newState);
        }

        // Show notifications
        static::showCompressionNotifications($successCount, $hasFailures);
    }

    /**
     * Compress temporary uploaded file
     */
    protected static function compressTemporaryFile(TemporaryUploadedFile $file, string $disk, ?string $directory): void
    {
        try {
            // Get the temporary file path
            $tempPath = $file->getRealPath();

            if (!$tempPath || !file_exists($tempPath)) {
                return;
            }

            // Load and process the image
            $manager = new \Intervention\Image\ImageManager(new \Intervention\Image\Drivers\Gd\Driver());
            $image = $manager->read($tempPath);

            // Get compression settings
            $compressionService = app(ImageCompressionService::class);
            $settings = $compressionService->getSettings();

            // Calculate new dimensions
            $originalWidth = $image->width();
            $originalHeight = $image->height();

            if ($originalWidth > $settings['max_width'] || $originalHeight > $settings['max_height']) {
                $widthRatio = $settings['max_width'] / $originalWidth;
                $heightRatio = $settings['max_height'] / $originalHeight;
                $scalingFactor = min($widthRatio, $heightRatio);

                $newWidth = (int) round($originalWidth * $scalingFactor);
                $newHeight = (int) round($originalHeight * $scalingFactor);

                $image->resize($newWidth, $newHeight);
            }

            // Convert to JPEG and compress
            $compressedImage = $image->toJpeg($settings['quality']);

            // Overwrite the temporary file with compressed version
            file_put_contents($tempPath, $compressedImage);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to compress temporary file', [
                'file' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Show compression notifications
     */
    protected static function showCompressionNotifications(int $successCount, bool $hasFailures): void
    {
        if ($successCount > 0 && config('image.compression.notifications.show_success', false)) {
            Notification::make()
                ->title('Kompresi Berhasil')
                ->body("{$successCount} foto berhasil dikompresi untuk menghemat ruang penyimpanan.")
                ->success()
                ->send();
        }

        if ($hasFailures && config('image.compression.notifications.show_failure', true)) {
            Notification::make()
                ->title('Peringatan Kompresi')
                ->body('Beberapa foto gagal dikompresi, namun tetap tersimpan dalam format original.')
                ->warning()
                ->send();
        }
    }
}
