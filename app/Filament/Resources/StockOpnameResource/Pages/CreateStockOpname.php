<?php

namespace App\Filament\Resources\StockOpnameResource\Pages;

use App\Filament\Resources\StockOpnameResource;
use App\Models\StockBatch;
use App\Models\StockMovement;
use App\Services\StockBatchService;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreateStockOpname extends CreateRecord
{
    protected static string $resource = StockOpnameResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if (!isset($data['user_id']) || empty($data['user_id'])) {
            $data['user_id'] = Auth::id();
        }
        return $data;
    }

    protected function afterCreate(): void
    {
        $record = $this->getRecord();
        $opnameDate = \Carbon\Carbon::parse($this->data['date_opname']);

        try {
            DB::beginTransaction();
            Log::info("Memulai proses stok opname #{$record->id}");

            $record->load('items');

            foreach ($record->items as $item) {
                $batch = StockBatch::find($item->batch_id);

                if (!$batch) {
                    Log::warning("Batch dengan ID {$item->batch_id} tidak ditemukan untuk opname item ID {$item->id}");
                    continue;
                }

                // Process each stock condition separately
                $hasChanges = false;

                // Process good stock condition
                $systemGoodBefore = $item->system_qty_good;
                $actualGood = $item->actual_qty_good;
                $differenceGood = $actualGood - $systemGoodBefore;

                if ($differenceGood != 0) {
                    $oldGoodQuantity = $batch->quantity;
                    $batch->quantity = $actualGood;
                    $hasChanges = true;

                    Log::info("Opname Item #{$item->id} - Good Stock: Product {$batch->product_id}, Batch {$batch->id}, System Qty: {$systemGoodBefore}, Actual Qty: {$actualGood}, Difference: {$differenceGood}");

                    StockMovement::create([
                        'batch_id' => $batch->id,
                        'user_id' => $record->user_id,
                        'quantity' => $differenceGood,
                        'quantity_after' => $actualGood,
                        'type' => 'OPNAME',
                        'description' => "OPNAME #{$record->id} - Good Stock at {$opnameDate->format('Y-m-d')}",
                        'movement_date' => $opnameDate,
                    ]);
                }

                // Process bad stock condition
                $systemBadBefore = $item->system_qty_bad;
                $actualBad = $item->actual_qty_bad;
                $differenceBad = $actualBad - $systemBadBefore;

                if ($differenceBad != 0) {
                    $oldBadQuantity = $batch->bad_quantity;
                    $batch->bad_quantity = $actualBad;
                    $hasChanges = true;

                    Log::info("Opname Item #{$item->id} - Bad Stock: Product {$batch->product_id}, Batch {$batch->id}, System Qty: {$systemBadBefore}, Actual Qty: {$actualBad}, Difference: {$differenceBad}");

                    StockMovement::create([
                        'batch_id' => $batch->id,
                        'user_id' => $record->user_id,
                        'quantity' => $differenceBad,
                        'quantity_after' => $actualBad,
                        'type' => 'OPNAME_BAD',
                        'description' => "OPNAME #{$record->id} - Bad Stock at {$opnameDate->format('Y-m-d')}",
                        'movement_date' => $opnameDate,
                    ]);
                }

                // Process unusable stock condition
                $systemUnusableBefore = $item->system_qty_unusable;
                $actualUnusable = $item->actual_qty_unusable;
                $differenceUnusable = $actualUnusable - $systemUnusableBefore;

                if ($differenceUnusable != 0) {
                    $oldUnusableQuantity = $batch->unusable_quantity;
                    $batch->unusable_quantity = $actualUnusable;
                    $hasChanges = true;

                    Log::info("Opname Item #{$item->id} - Unusable Stock: Product {$batch->product_id}, Batch {$batch->id}, System Qty: {$systemUnusableBefore}, Actual Qty: {$actualUnusable}, Difference: {$differenceUnusable}");

                    StockMovement::create([
                        'batch_id' => $batch->id,
                        'user_id' => $record->user_id,
                        'quantity' => $differenceUnusable,
                        'quantity_after' => $actualUnusable,
                        'type' => 'OPNAME_UNUSABLE',
                        'description' => "OPNAME #{$record->id} - Unusable Stock at {$opnameDate->format('Y-m-d')}",
                        'movement_date' => $opnameDate,
                    ]);
                }

                if ($hasChanges) {
                    $batch->save();
                    Log::info("Stok batch {$batch->id} berhasil diupdate - Good: {$batch->quantity}, Bad: {$batch->bad_quantity}, Unusable: {$batch->unusable_quantity}");
                } else {
                    Log::info("Tidak ada perubahan stok untuk batch {$batch->id}");
                }
            }

            DB::commit();
            Log::info("Proses stok opname #{$record->id} selesai dengan sukses");

            Notification::make()
                ->title('Stok berhasil diperbarui')
                ->success()
                ->send();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error saat memproses stok opname #{$record->id}: " . $e->getMessage());
            Log::error($e->getTraceAsString());

            Notification::make()
                ->title('Terjadi kesalahan saat memproses stok opname')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
