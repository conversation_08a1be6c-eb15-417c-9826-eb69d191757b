<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StockBatchResource\Pages;
use App\Filament\Resources\StockBatchResource\RelationManagers;
use App\Models\Product;
use App\Models\StockBatch;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StockBatchResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-archive-box-arrow-down';

    protected static ?string $navigationGroup = 'Manajemen Stok';

    protected static ?string $modelLabel = 'Cek Stok';

    protected static ?string $pluralModelLabel = 'Cek Stok';

    protected static ?string $slug = 'cek-stok';

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function table(Table $table): Table
    {
        $user = auth()->user();
        $canViewPricing = $user?->canViewPricing() ?? false;

        return $table
            ->query(
                Product::query()
                    ->with(['brand', 'stockBatches'])
                    ->select('products.id', 'products.name', 'products.brand_id', 'products.type')
                    ->withSum(['stockBatches' => function ($query) {
                    }], 'quantity')
                    ->withSum(['stockBatches' => function ($query) {
                    }], 'bad_quantity')
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Produk')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('brand.name')
                    ->label('Merek')
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('Jenis')
                    ->formatStateUsing(fn (string $state): string => Product::TYPES[$state] ?? $state)
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'single' => 'gray',
                        'package' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('stock_batches_sum_quantity')
                    ->label('Stok Normal')
                    ->numeric()
                    ->sortable()
                    ->state(function (Product $record): int {
                        // Untuk produk paket, tidak menampilkan stok normal karena tidak relevan
                        return $record->isPackage() ? 0 : ($record->stock_batches_sum_quantity ?? 0);
                    })
                    ->icon(fn (string | null $state): string | null => match (true) {
                        $state > 0 && $state < 10 => 'heroicon-o-exclamation-triangle',
                        default => null,
                    })
                    ->iconPosition('after')
                    ->iconColor('warning')
                    ->formatStateUsing(function (int $state, Product $record): string {
                        return $record->isPackage() ? '-' : number_format($state);
                    }),
                Tables\Columns\TextColumn::make('reserved_normal_stock')
                    ->label('Stok Direservasi')
                    ->numeric()
                    ->sortable()
                    ->state(function (Product $record): int {
                        if ($record->isPackage()) {
                            return 0;
                        }
                        $availableStockService = app(\App\Services\AvailableStockService::class);
                        return $availableStockService->getReservedNormalStock($record->id);
                    })
                    ->formatStateUsing(function (int $state, Product $record): string {
                        return $record->isPackage() ? '-' : number_format($state);
                    })
                    ->color('warning'),
                Tables\Columns\TextColumn::make('available_normal_stock')
                    ->label('Stok Tersedia')
                    ->numeric()
                    ->sortable()
                    ->state(function (Product $record): int {
                        if ($record->isPackage()) {
                            $availableStockService = app(\App\Services\AvailableStockService::class);
                            return $availableStockService->getAvailablePackageStock($record);
                        }
                        $availableStockService = app(\App\Services\AvailableStockService::class);
                        return $availableStockService->getAvailableNormalStock($record->id);
                    })
                    ->formatStateUsing(function (int $state, Product $record): string {
                        if ($record->isPackage()) {
                            return number_format($state) . ' paket';
                        }
                        return number_format($state);
                    })
                    ->color(fn (int $state): string => match (true) {
                        $state === 0 => 'danger',
                        $state < 10 => 'warning',
                        default => 'success',
                    }),
                Tables\Columns\TextColumn::make('stock_batches_sum_bad_quantity')
                    ->label('Stok Buruk')
                    ->numeric()
                    ->sortable()
                    ->state(function (Product $record): int {
                        // Untuk produk paket, tidak menampilkan stok buruk karena tidak relevan
                        return $record->isPackage() ? 0 : ($record->stock_batches_sum_bad_quantity ?? 0);
                    })
                    ->formatStateUsing(function (int $state, Product $record): string {
                        return $record->isPackage() ? '-' : number_format($state);
                    }),
                Tables\Columns\TextColumn::make('reserved_bad_stock')
                    ->label('Stok Buruk Direservasi')
                    ->numeric()
                    ->sortable()
                    ->state(function (Product $record): int {
                        if ($record->isPackage()) {
                            return 0;
                        }
                        $availableStockService = app(\App\Services\AvailableStockService::class);
                        return $availableStockService->getReservedBadStock($record->id);
                    })
                    ->formatStateUsing(function (int $state, Product $record): string {
                        return $record->isPackage() ? '-' : number_format($state);
                    })
                    ->color('warning')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('available_bad_stock')
                    ->label('Stok Buruk Tersedia')
                    ->numeric()
                    ->sortable()
                    ->state(function (Product $record): int {
                        if ($record->isPackage()) {
                            return 0;
                        }
                        $availableStockService = app(\App\Services\AvailableStockService::class);
                        return $availableStockService->getAvailableBadStock($record->id);
                    })
                    ->formatStateUsing(function (int $state, Product $record): string {
                        return $record->isPackage() ? '-' : number_format($state);
                    })
                    ->color(fn (int $state): string => match (true) {
                        $state === 0 => 'gray',
                        $state < 5 => 'warning',
                        default => 'danger',
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('total_stock')
                    ->label('Total Stok')
                    ->numeric()
                    ->sortable()
                    ->state(function (Product $record): int {
                        if ($record->isPackage()) {
                            // Untuk produk paket, gunakan estimasi stok paket
                            return $record->getEstimatedPackageStock();
                        } else {
                            // Untuk produk satuan, gunakan total stok normal + buruk
                            return ($record->stock_batches_sum_quantity ?? 0) + ($record->stock_batches_sum_bad_quantity ?? 0);
                        }
                    })
                    ->formatStateUsing(function (int $state, Product $record): string {
                        $formattedState = number_format($state);
                        if ($record->isPackage()) {
                            return $formattedState . ' paket';
                        }
                        return $formattedState;
                    })
                    ->badge()
                    ->color(function (int $state, Product $record): string {
                        if ($state === 0) {
                            return 'danger';
                        } elseif ($state < 10) {
                            return 'warning';
                        } else {
                            return 'success';
                        }
                    })
                    ->icon(function (int $state, Product $record): string {
                        if ($record->isPackage()) {
                            return 'heroicon-m-cube';
                        } else {
                            return 'heroicon-m-cube-transparent';
                        }
                    }),

                // Kolom untuk Owner dan Store Manager
                Tables\Columns\TextColumn::make('stock_expiring_1_year')
                    ->label('Expired < 1 Tahun')
                    ->visible($canViewPricing)
                    ->state(function (Product $record): int {
                        if ($record->isPackage()) {
                            return 0;
                        }

                        $oneYearFromNow = Carbon::now()->addYear();
                        $total = 0;
                        foreach ($record->stockBatches as $batch) {
                            if ($batch->expiration_date && $batch->expiration_date->lte($oneYearFromNow)) {
                                $total += $batch->quantity + $batch->bad_quantity;
                            }
                        }
                        return $total;
                    })
                    ->numeric()
                    ->badge()
                    ->color(fn (int $state): string => $state > 0 ? 'warning' : 'gray'),

                Tables\Columns\TextColumn::make('stock_expiring_6_months')
                    ->label('Expired < 6 Bulan')
                    ->visible($canViewPricing)
                    ->state(function (Product $record): int {
                        if ($record->isPackage()) {
                            return 0;
                        }

                        $sixMonthsFromNow = Carbon::now()->addMonths(6);
                        $total = 0;
                        foreach ($record->stockBatches as $batch) {
                            if ($batch->expiration_date && $batch->expiration_date->lte($sixMonthsFromNow)) {
                                $total += $batch->quantity + $batch->bad_quantity;
                            }
                        }
                        return $total;
                    })
                    ->numeric()
                    ->badge()
                    ->color(fn (int $state): string => $state > 0 ? 'danger' : 'gray'),

                Tables\Columns\TextColumn::make('stock_expired')
                    ->label('Expired')
                    ->visible($canViewPricing)
                    ->state(function (Product $record): int {
                        if ($record->isPackage()) {
                            return 0;
                        }

                        $now = Carbon::now();
                        $total = 0;
                        foreach ($record->stockBatches as $batch) {
                            if ($batch->expiration_date && $batch->expiration_date->lt($now)) {
                                $total += $batch->quantity + $batch->bad_quantity;
                            }
                        }
                        return $total;
                    })
                    ->numeric()
                    ->badge()
                    ->color(fn (int $state): string => $state > 0 ? 'danger' : 'gray'),

                Tables\Columns\TextColumn::make('stock_value')
                    ->label('Nilai Stok')
                    ->visible($canViewPricing)
                    ->state(function (Product $record): float {
                        if ($record->isPackage()) {
                            return 0; // Paket tidak memiliki nilai stok langsung
                        }

                        $totalValue = 0;
                        foreach ($record->stockBatches as $batch) {
                            $totalStock = $batch->quantity + $batch->bad_quantity;
                            $totalValue += $totalStock * $batch->purchase_price;
                        }
                        return $totalValue;
                    })
                    ->money('IDR')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('brand_id')
                    ->label('Merek')
                    ->relationship('brand', 'name'),
                Tables\Filters\SelectFilter::make('type')
                    ->label('Jenis Produk')
                    ->options(Product::TYPES),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Lihat Detail Stok'),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ProductStockBatchesRelationManager::class,
            RelationManagers\PackageComponentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductStock::route('/'),
            'view' => Pages\ViewProductStock::route('/{record}'),
            'history' => Pages\StockMovementHistoryPage::route('/batch/{record}/history'),
        ];
    }
}
