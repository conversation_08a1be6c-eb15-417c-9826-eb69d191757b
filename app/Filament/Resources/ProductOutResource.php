<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductOutResource\Pages;
use App\Filament\Resources\ProductOutResource\RelationManagers;
use App\Models\ProductOut;
use App\Models\ProductOutStatus;
use App\Models\Product;
use App\Models\StockBatch;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Support\RawJs;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\DatePicker;
use App\Models\SalesChannel;
use App\Models\Employee;
use App\Services\DiscountService;
use App\Filament\Components\CompressedFileUpload;

class ProductOutResource extends Resource
{
    protected static ?string $model = ProductOut::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-up-tray';

    protected static ?string $modelLabel = 'Produk Keluar';

    protected static ?string $pluralModelLabel = 'Produk Keluar';

    protected static ?string $navigationGroup = 'Transaksi';

    public static function getNavigationBadge(): ?string
    {
        $user = auth()->user();
        if (!$user) return null;

        $count = 0;

        if ($user->isStoreManager()) {
            $count = static::getModel()::where('status', ProductOutStatus::WAITING_STORE_MANAGER_APPROVAL)->count();
        } elseif ($user->isOwner()) {
            $count = static::getModel()::where('status', ProductOutStatus::WAITING_OWNER_APPROVAL)->count();
        }

        return $count > 0 ? (string) $count : null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public static function canEdit(Model $record): bool
    {
        $user = auth()->user();
        if (!$user) return false;

        // Admin packing hanya bisa edit record milik sendiri yang belum berubah ke menunggu persetujuan owner
        if ($user->isPackingAdmin()) {
            return $record->user_id === $user->id &&
                   $record->status === ProductOutStatus::WAITING_STORE_MANAGER_APPROVAL;
        }

        // Store manager dan owner bisa edit sebelum melakukan approval (status tidak berubah setelah edit)
        if ($user->hasFullAccess()) {
            return in_array($record->status, [
                ProductOutStatus::WAITING_STORE_MANAGER_APPROVAL,
                ProductOutStatus::WAITING_OWNER_APPROVAL
            ]);
        }

        return false;
    }

    public static function form(Form $form): Form
    {
        $isEditMode = $form->getOperation() === 'edit';

        return $form
            ->schema([
                Forms\Components\DateTimePicker::make('date_out')
                    ->label('Tanggal & Waktu Keluar')
                    ->required()
                    ->default(now())
                    ->seconds(false)
                    ->disabled($isEditMode),
                Forms\Components\Select::make('channel_id')
                    ->label('Saluran Penjualan')
                    ->relationship('salesChannel', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->live()
                    ->disabled($isEditMode)
                    ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                        // Update auto discount untuk semua items ketika saluran penjualan berubah
                        $items = $get('items') ?? [];
                        foreach ($items as $index => $item) {
                            if (!empty($item['product_id']) && !($item['use_manual_discount'] ?? false)) {
                                $productId = $item['product_id'];
                                $stockType = $item['stock_type'] ?? 'normal';
                                $condition = $stockType === 'bad' ? 'bad' : ($stockType === 'package' ? null : 'good');

                                $discountService = app(\App\Services\DiscountService::class);
                                $discountData = $discountService->getDiscountedPrice($productId, $condition, $state);

                                if ($discountData['has_discount']) {
                                    $set("items.{$index}.discount_id", $discountData['discount']->discount_id);
                                    $set("items.{$index}.final_price", number_format($discountData['final_price'], 0, ',', '.'));
                                    $set("items.{$index}.is_manual_discount", false);
                                } else {
                                    $set("items.{$index}.discount_id", null);
                                    $set("items.{$index}.final_price", null);
                                    $set("items.{$index}.is_manual_discount", false);
                                }
                            }
                        }
                    }),
                Forms\Components\Textarea::make('note')
                    ->label('Catatan')
                    ->columnSpanFull()
                    ->disabled($isEditMode),
                CompressedFileUpload::evidencePhotos('evidence_photos')
                    ->directory('product-out-evidence')
                    ->disabled($isEditMode),
                Forms\Components\Hidden::make('user_id')
                    ->default(fn() => Auth::id()),
                Forms\Components\Repeater::make('items')
                    ->label('Item Produk')
                    ->helperText('Pilih produk normal, "[BAD] Produk" untuk stok buruk, atau "[PAKET] Produk" untuk produk paket. Sistem akan otomatis memilih batch stok berdasarkan FEFO (First Expired First Out) dan LCOF (Landed Cost Optimization First). Untuk paket, sistem akan otomatis mengurangi stok komponen sesuai komposisi.')
                    ->schema([
                        Forms\Components\Select::make('product_selection')
                            ->label('Produk')
                            ->options(function () {
                                $availableStockService = new \App\Services\AvailableStockService();
                                return $availableStockService->getProductsForDropdown();
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->disabled($isEditMode)
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                // Reset batch_id dan discount fields
                                $set('batch_id', null);
                                $set('discount_id', null);
                                $set('final_price', null);
                                $set('is_manual_discount', false);

                                if ($state) {
                                    // Extract product ID and stock type from selection
                                    $productId = null;
                                    $stockType = 'normal';
                                    $condition = null;

                                    if (str_starts_with($state, 'normal_')) {
                                        $productId = str_replace('normal_', '', $state);
                                        $stockType = 'normal';
                                        $condition = 'good';
                                    } elseif (str_starts_with($state, 'bad_')) {
                                        $productId = str_replace('bad_', '', $state);
                                        $stockType = 'bad';
                                        $condition = 'bad';
                                    } elseif (str_starts_with($state, 'package_')) {
                                        $productId = str_replace('package_', '', $state);
                                        $stockType = 'package';
                                        $condition = null; // Package tidak punya kondisi
                                    }

                                    // Set hidden fields
                                    $set('product_id', $productId);
                                    $set('stock_type', $stockType);

                                    // Set default price if needed
                                    if ($productId && $get('use_default_price')) {
                                        $product = \App\Models\Product::find($productId);
                                        if ($product) {
                                            $set('sale_price', number_format($product->default_sale_price, 0, ',', '.'));
                                        }
                                    }

                                    // Check for active discount
                                    if ($productId) {
                                        $discountService = app(DiscountService::class);
                                        // Get channel_id from parent form
                                        $channelId = $get('../../channel_id');
                                        $discountData = $discountService->getDiscountedPrice($productId, $condition, $channelId);

                                        if ($discountData['has_discount']) {
                                            $set('discount_id', $discountData['discount']->discount_id);
                                            $set('final_price', number_format($discountData['final_price'], 0, ',', '.'));
                                            $set('is_manual_discount', false);
                                        }
                                    }
                                }
                            }),
                        // Hidden fields untuk menyimpan product_id dan stock_type yang sebenarnya
                        Forms\Components\Hidden::make('product_id'),
                        Forms\Components\Hidden::make('stock_type'),
                        Forms\Components\Hidden::make('batch_id')
                            ->default(null),
                        Forms\Components\Hidden::make('discount_id')
                            ->dehydrated(true),
                        Forms\Components\Hidden::make('is_manual_discount')
                            ->default(false)
                            ->dehydrated(true),
                        Forms\Components\TextInput::make('quantity')
                            ->label('Jumlah')
                            ->numeric()
                            ->required()
                            ->minValue(1)
                            ->disabled($isEditMode),
                        Forms\Components\Checkbox::make('use_default_price')
                            ->label('Gunakan Harga Default Jual')
                            ->default(true)
                            ->live()
                            ->dehydrated(false)
                            ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set) {
                                $productId = $get('product_id');
                                if ($state && $productId) {
                                    $product = \App\Models\Product::find($productId);
                                    if ($product) {
                                        $set('sale_price', number_format($product->default_sale_price, 0, ',', '.'));
                                    }
                                }
                            }),
                        Forms\Components\TextInput::make('sale_price')
                            ->label('Harga Jual')
                            ->numeric()
                            ->prefix('Rp')
                            ->mask(RawJs::make(<<<'JS'
                            $money($input, ',', '.', 2)
                            JS))
                            ->stripCharacters('.')
                            ->dehydrateStateUsing(fn ($state) =>
                                is_string($state) ? str_replace(',', '.', str_replace('.', '', $state)) : $state
                            )
                            ->formatStateUsing(fn ($state): ?string => number_format((float)$state, 0, ',', '.'))
                            ->required()
                            ->disabled(fn (Forms\Get $get): bool => $get('use_default_price') ?? true)
                            ->dehydrated(true),
                        Forms\Components\Checkbox::make('use_manual_discount')
                            ->label('Input Diskon Manual')
                            ->default(false)
                            ->live()
                            ->dehydrated(false)
                            ->disabled($isEditMode)
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                if ($state) {
                                    // Jika manual discount diaktifkan, reset auto discount
                                    $set('discount_id', null);
                                    $set('is_manual_discount', true);

                                    // Set final_price ke sale_price sebagai starting point
                                    $salePrice = $get('sale_price');
                                    if ($salePrice) {
                                        $cleanPrice = str_replace(['.', ','], ['', '.'], $salePrice);
                                        $set('final_price', number_format((float)$cleanPrice, 0, ',', '.'));
                                    }
                                } else {
                                    // Jika manual discount dimatikan, cek auto discount lagi
                                    $set('is_manual_discount', false);
                                    $productId = $get('product_id');
                                    $stockType = $get('stock_type');

                                    if ($productId) {
                                        $condition = $stockType === 'bad' ? 'bad' : ($stockType === 'package' ? null : 'good');
                                        $discountService = app(DiscountService::class);
                                        // Get channel_id from parent form
                                        $channelId = $get('../../channel_id');
                                        $discountData = $discountService->getDiscountedPrice($productId, $condition, $channelId);

                                        if ($discountData['has_discount']) {
                                            $set('discount_id', $discountData['discount']->discount_id);
                                            $set('final_price', number_format($discountData['final_price'], 0, ',', '.'));
                                        } else {
                                            $set('discount_id', null);
                                            $set('final_price', null);
                                        }
                                    }
                                }
                            }),
                        Forms\Components\TextInput::make('final_price')
                            ->label('Harga Akhir')
                            ->numeric()
                            ->prefix('Rp')
                            ->mask(RawJs::make(<<<'JS'
                            $money($input, ',', '.', 2)
                            JS))
                            ->stripCharacters('.')
                            ->dehydrateStateUsing(function ($state) {
                                if (empty($state)) {
                                    return null;
                                }
                                return is_string($state) ? str_replace(',', '.', str_replace('.', '', $state)) : $state;
                            })
                            ->formatStateUsing(fn ($state): ?string => $state ? number_format((float)$state, 0, ',', '.') : null)
                            ->disabled(fn (Forms\Get $get): bool => !($get('use_manual_discount') ?? false) || $isEditMode)
                            ->helperText(function (Forms\Get $get) {
                                $discountId = $get('discount_id');
                                if ($discountId && !($get('use_manual_discount') ?? false)) {
                                    // Get discount name
                                    $discount = \App\Models\Discount::find($discountId);
                                    $discountName = $discount ? $discount->name : 'Unknown';
                                    return "💰 Diskon {$discountName} diterapkan";
                                }
                                return $get('use_manual_discount') ? 'Masukkan harga akhir setelah diskon' : 'Tidak ada diskon aktif';
                            })
                            ->reactive()
                            ->dehydrated(true)
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                if ($get('use_manual_discount') && $state) {
                                    $set('is_manual_discount', true);
                                }
                            }),
                    ])
                    ->columns(2)
                    ->required()
                    ->minItems(1)
                    ->disabled($isEditMode)
                    ->columnSpanFull()
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Produk Keluar')
                    ->schema([
                        TextEntry::make('date_out')
                            ->label('Tanggal & Waktu Keluar')
                            ->dateTime('d M Y H:i'),
                        TextEntry::make('salesChannel.name')
                            ->label('Saluran Penjualan'),
                        TextEntry::make('user.name')
                            ->label('Petugas'),
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn (ProductOutStatus $state): string => $state->getColor())
                            ->formatStateUsing(fn (ProductOutStatus $state): string => $state->getLabel()),
                        TextEntry::make('note')
                            ->label('Catatan')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                Section::make('Informasi Persetujuan')
                    ->schema([
                        TextEntry::make('managerApprovedBy.name')
                            ->label('Disetujui Kepala Toko')
                            ->placeholder('Belum disetujui'),
                        TextEntry::make('manager_approved_at')
                            ->label('Tanggal Persetujuan Kepala Toko')
                            ->dateTime('d M Y H:i')
                            ->placeholder('Belum disetujui'),
                        TextEntry::make('ownerApprovedBy.name')
                            ->label('Disetujui Owner')
                            ->placeholder('Belum disetujui'),
                        TextEntry::make('owner_approved_at')
                            ->label('Tanggal Persetujuan Owner')
                            ->dateTime('d M Y H:i')
                            ->placeholder('Belum disetujui'),
                        TextEntry::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->columnSpanFull()
                            ->visible(fn ($record) => $record->isRejected()),
                    ])
                    ->columns(2),
                Section::make('Foto Bukti')
                    ->schema([
                        ImageEntry::make('evidence_photos')
                            ->label('Foto Bukti')
                            ->disk('public')
                            ->height(200)
                            ->width(300)
                            ->columnSpanFull()
                            ->stacked()
                            ->extraAttributes([
                                'class' => 'lightbox-image-gallery',
                                'style' => 'cursor: pointer;'
                            ])
                            ->visible(fn ($record) => !empty($record->evidence_photos)),
                    ])
                    ->visible(fn ($record) => !empty($record->evidence_photos))
                    ->collapsed()
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->modifyQueryUsing(function (Builder $query) {
                $user = auth()->user();
                if ($user && !$user->canViewOthersRecords()) {
                    $query->where('user_id', $user->id);
                }
                return $query;
            })
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('date_out')
                    ->label('Tanggal & Waktu Keluar')
                    ->dateTime('d M Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('salesChannel.name')
                    ->label('Saluran Penjualan')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('items_count')
                    ->label('Jumlah Produk')
                    ->counts('items')
                    ->sortable(),
                Tables\Columns\TextColumn::make('items_sum_quantity')
                    ->label('Total Kuantitas')
                    ->sum('items', 'quantity')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Petugas')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ProductOutStatus $state): string => $state->getColor())
                    ->formatStateUsing(fn (ProductOutStatus $state): string => $state->getLabel())
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('date_out')
                    ->form([
                        DatePicker::make('created_from')->label('Dari Tanggal'),
                        DatePicker::make('created_until')->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_out', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_out', '<=', $date),
                            );
                    }),
                SelectFilter::make('status')
                    ->label('Status')
                    ->options(ProductOutStatus::getOptions()),
                SelectFilter::make('channel_id')
                    ->label('Saluran Penjualan')
                    ->relationship('salesChannel', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('user_id')
                    ->label('Petugas')
                    ->options(Employee::query()->pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn (Model $record): bool => static::canEdit($record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(), // dihapus agar tidak bisa hapus massal
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductOuts::route('/'),
            'create' => Pages\CreateProductOut::route('/create'),
            'view' => Pages\ViewProductOut::route('/{record}'),
            'edit' => Pages\EditProductOut::route('/{record}/edit'),
            'approval' => Pages\ApprovalProductOut::route('/{record}/approval'),
        ];
    }
}
