<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\StockBatch;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StockPdfController extends Controller
{
    /**
     * Download PDF yang menampilkan produk dengan total stoknya
     */
    public function downloadTotalStock()
    {
        $user = auth()->user();
        $canViewPricing = $user?->canViewPricing() ?? false;

        // Ambil produk satuan dan paket secara terpisah
        $singleProducts = Product::query()
            ->with(['brand', 'stockBatches'])
            ->select('products.id', 'products.name', 'products.brand_id', 'products.type')
            ->where('type', 'single')
            ->withSum(['stockBatches'], 'quantity')
            ->withSum(['stockBatches'], 'bad_quantity')
            ->withSum(['stockBatches'], 'unusable_quantity')
            ->get()
            ->map(function ($product) use ($canViewPricing) {
                // Hitung nilai stok
                $stockValue = 0;
                $stockExpiring1Year = 0;
                $stockExpiring6Months = 0;
                $stockExpired = 0;

                if (!$product->isPackage()) {
                    $oneYearFromNow = Carbon::now()->addYear();
                    $sixMonthsFromNow = Carbon::now()->addMonths(6);
                    $now = Carbon::now();

                    foreach ($product->stockBatches as $batch) {
                        $totalStock = $batch->quantity + $batch->bad_quantity;

                        // Nilai stok - hanya hitung jika user bisa melihat pricing
                        if ($canViewPricing) {
                            $stockValue += $totalStock * $batch->purchase_price;
                        }

                        // Stok expired < 1 tahun
                        if ($batch->expiration_date && $batch->expiration_date->lte($oneYearFromNow)) {
                            $stockExpiring1Year += $totalStock;
                        }

                        // Stok expired < 6 bulan
                        if ($batch->expiration_date && $batch->expiration_date->lte($sixMonthsFromNow)) {
                            $stockExpiring6Months += $totalStock;
                        }

                        // Stok expired
                        if ($batch->expiration_date && $batch->expiration_date->lt($now)) {
                            $stockExpired += $totalStock;
                        }
                    }
                }

                $product->stock_value = $stockValue;
                $product->stock_expiring_1_year = $stockExpiring1Year;
                $product->stock_expiring_6_months = $stockExpiring6Months;
                $product->stock_expired = $stockExpired;

                return $product;
            });

        // Ambil produk paket secara terpisah
        $packageProducts = Product::query()
            ->with(['brand', 'packageCompositions.component'])
            ->select('products.id', 'products.name', 'products.brand_id', 'products.type')
            ->where('type', 'package')
            ->get()
            ->map(function ($product) {
                $product->estimated_package_stock = $product->getEstimatedPackageStock();
                return $product;
            });

        $pdf = Pdf::loadView('pdf.total-stock', [
            'singleProducts' => $singleProducts,
            'packageProducts' => $packageProducts,
            'date' => now()->format('d/m/Y H:i'),
            'canViewPricing' => $canViewPricing,
        ]);

        return $pdf->download('laporan_stok_total_' . now()->format('d-m-Y_His') . '.pdf');
    }

    /**
     * Download PDF yang menampilkan stok per batch dan dikelompokkan berdasarkan produknya
     */
    public function downloadBatchStock()
    {
        $user = auth()->user();
        $canViewPricing = $user?->canViewPricing() ?? false;

        // Hanya ambil produk satuan untuk PDF batch stock
        $products = Product::with(['brand', 'stockBatches' => function ($query) {
            $query->where(function ($q) {
                $q->where('quantity', '>', 0)
                  ->orWhere('bad_quantity', '>', 0)
                  ->orWhere('unusable_quantity', '>', 0);
            })->orderBy('expiration_date');
        }])
        ->where('type', 'single') // Hanya produk satuan
        ->get();

        $pdf = Pdf::loadView('pdf.batch-stock', [
            'products' => $products,
            'date' => now()->format('d/m/Y H:i'),
            'canViewPricing' => $canViewPricing,
        ]);

        return $pdf->download('laporan_stok_batch_' . now()->format('d-m-Y_His') . '.pdf');
    }
}
