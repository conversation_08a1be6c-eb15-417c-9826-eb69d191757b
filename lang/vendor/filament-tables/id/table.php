<?php

return [

    'column_toggle' => [

        'heading' => 'Kolom',

    ],

    'columns' => [

        'actions' => [
            'label' => 'Aksi|Aksi',
        ],

        'text' => [

            'actions' => [
                'collapse_list' => 'Sembunyikan :count lainnya',
                'expand_list' => 'Tampilkan :count lainnya',
            ],

            'more_list_items' => 'dan :count lagi',

        ],

    ],

    'fields' => [

        'bulk_select_page' => [
            'label' => 'Buat/batalkan pilihan semua item untuk tindakan massal.',
        ],

        'bulk_select_record' => [
            'label' => 'Buat/batalkan pilihan item :key untuk tindakan massal.',
        ],

        'bulk_select_group' => [
            'label' => 'Buat/batalkan pilihan grup :title untuk tindakan massal.',
        ],

        'search' => [
            'label' => 'Cari',
            'placeholder' => 'Cari',
            'indicator' => 'Pencarian',
        ],

    ],

    'summary' => [

        'heading' => 'Rangkuman',

        'subheadings' => [
            'all' => 'Semua :label',
            'group' => 'Rangkuman :group',
            'page' => 'Halaman ini',
        ],

        'summarizers' => [

            'average' => [
                'label' => 'Rata-rata',
            ],

            'count' => [
                'label' => 'Jumlah',
            ],

            'sum' => [
                'label' => 'Total',
            ],

        ],

    ],

    'actions' => [

        'disable_reordering' => [
            'label' => 'Selesaikan pengurutan ulang',
        ],

        'enable_reordering' => [
            'label' => 'Urutkan ulang',
        ],

        'filter' => [
            'label' => 'Filter',
        ],

        'group' => [
            'label' => 'Grup',
        ],

        'open_bulk_actions' => [
            'label' => 'Tindakan massal',
        ],

        'toggle_columns' => [
            'label' => 'Kolom',
        ],

    ],

    'empty' => [

        'heading' => 'Tidak ada data',

        'description' => 'Buat :model untuk memulai.',

    ],

    'filters' => [

        'actions' => [

            'apply' => [
                'label' => 'Terapkan filter',
            ],

            'remove' => [
                'label' => 'Hapus filter',
            ],

            'remove_all' => [
                'label' => 'Hapus semua filter',
                'tooltip' => 'Hapus semua filter',
            ],

            'reset' => [
                'label' => 'Atur ulang filter',
            ],

        ],

        'heading' => 'Filter',

        'indicator' => 'Filter aktif',

        'multi_select' => [
            'placeholder' => 'Semua',
        ],

        'select' => [
            'placeholder' => 'Semua',
        ],

        'trashed' => [

            'label' => 'Data terhapus',

            'only_trashed' => 'Hanya data terhapus',

            'with_trashed' => 'Dengan data terhapus',

            'without_trashed' => 'Tanpa data terhapus',

        ],

    ],

    'grouping' => [

        'fields' => [

            'group' => [
                'label' => 'Kelompokkan berdasar',
                'placeholder' => 'Kelompokkan berdasar',
            ],

            'direction' => [

                'label' => 'Urutan grup',

                'options' => [
                    'asc' => 'Naik',
                    'desc' => 'Turun',
                ],

            ],

        ],

    ],

    'reorder_indicator' => 'Seret dan lepaskan data ke dalam urutan.',

    'selection_indicator' => [

        'selected_count' => '1 data dipilih|:count data dipilih',

        'actions' => [

            'select_all' => [
                'label' => 'Pilih semua (:count)',
            ],

            'deselect_all' => [
                'label' => 'Batalkan semua pilihan',
            ],

        ],

    ],

    'sorting' => [

        'fields' => [

            'column' => [
                'label' => 'Urutkan menurut',
            ],

            'direction' => [

                'label' => 'Arah urutan',

                'options' => [
                    'asc' => 'Naik',
                    'desc' => 'Turun',
                ],

            ],

        ],

    ],

    'pagination' => [

        'buttons' => [

            'go_to_page' => [
                'label' => 'Ke halaman :page',
            ],

            'next' => [
                'label' => 'Selanjutnya',
            ],

            'previous' => [
                'label' => 'Sebelumnya',
            ],

        ],

        'label' => 'Navigasi Halaman',

        'overview' => 'Menampilkan :first sampai :last dari :total hasil',

        'items_per_page' => [
            'label' => 'Item per halaman',
        ],

    ],

    'search' => [
        'field' => 'Cari',
        'placeholder' => 'Cari',
    ],

];
