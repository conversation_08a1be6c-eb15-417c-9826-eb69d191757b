@php
    $brandName = filament()->getBrandName();
    $brandLogo = filament()->getBrandLogo();
    $brandLogoHeight = filament()->getBrandLogoHeight() ?? '1.5rem';
    $darkModeBrandLogo = filament()->getDarkModeBrandLogo();
    $hasDarkModeBrandLogo = filled($darkModeBrandLogo);

    // Check if we're on the login page for larger logo
    $isLoginPage = request()->routeIs('filament.admin.auth.login');
    $logoHeight = $isLoginPage ? '6rem' : $brandLogoHeight;

    // Check if we're in the sidebar context
    $isSidebar = request()->routeIs('filament.*') && !$isLoginPage;

    $getLogoClasses = fn (bool $isDarkMode): string => \Illuminate\Support\Arr::toCssClasses([
        'fi-logo',
        'flex items-center gap-3' => $isSidebar && ! $hasDarkModeBrandLogo,
        'flex items-center gap-3 dark:hidden' => $isSidebar && $hasDarkModeBrandLogo && (! $isDarkMode),
        'hidden dark:flex items-center gap-3' => $isSidebar && $hasDarkModeBrandLogo && $isDarkMode,
        'flex' => !$isSidebar && ! $hasDarkModeBrandLogo,
        'flex dark:hidden' => !$isSidebar && $hasDarkModeBrandLogo && (! $isDarkMode),
        'hidden dark:flex' => !$isSidebar && $hasDarkModeBrandLogo && $isDarkMode,
    ]);

    $logoStyles = "height: {$logoHeight}";
@endphp

@capture($content, $logo, $isDarkMode = false)
    @if ($logo instanceof \Illuminate\Contracts\Support\Htmlable)
        <div
            {{
                $attributes
                    ->class([$getLogoClasses($isDarkMode)])
                    ->style([$logoStyles])
            }}
        >
            {{ $logo }}
        </div>
    @elseif (filled($logo))
        <div
            {{
                $attributes->class([$getLogoClasses($isDarkMode)])
            }}
        >
            <img
                alt="{{ __('filament-panels::layout.logo.alt', ['name' => $brandName]) }}"
                src="{{ $logo }}"
                style="{{ $logoStyles }}"
            />
            @if ($isSidebar)
                <span class="font-semibold leading-5 tracking-tight text-gray-950 dark:text-white">
                    {{ $brandName }}
                </span>
            @endif
        </div>
    @else
        <div
            {{
                $attributes->class([
                    $getLogoClasses($isDarkMode),
                    'text-xl font-bold leading-5 tracking-tight text-gray-950 dark:text-white',
                ])
            }}
        >
            {{ $brandName }}
        </div>
    @endif
@endcapture

{{ $content($brandLogo) }}

@if ($hasDarkModeBrandLogo)
    {{ $content($darkModeBrandLogo, isDarkMode: true) }}
@endif
