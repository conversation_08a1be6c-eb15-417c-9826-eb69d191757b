<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title><PERSON><PERSON><PERSON>ok Total</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.3;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            padding: 0;
            font-size: 18px;
        }
        .meta {
            margin-bottom: 15px;
            text-align: right;
            font-size: 11px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: left;
            font-size: 9px;
        }
        table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            font-size: 10px;
            margin-top: 30px;
            color: #666;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .total-row {
            background-color: #e8f4f8;
            font-weight: bold;
        }
        .total-row td {
            border-top: 2px solid #333;
        }
        .badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
            color: black;
            text-align: center;
        }
        .badge-warning {
            background-color:#fde68a;
        }
        .badge-danger {
            background-color: #fecaca;
        }
        .badge-gray {
            background-color: #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>LAPORAN STOK TOTAL</h1>
    </div>

    <div class="meta">
        Tanggal Laporan: {{ $date }}
    </div>

    <!-- Tabel Produk Satuan -->
    <h2 style="font-size: 14px; margin-bottom: 10px; margin-top: 20px;">PRODUK SATUAN</h2>
    <table>
        <thead>
            <tr>
                <th class="text-center" width="3%">No</th>
                <th width="{{ $canViewPricing ? '15%' : '18%' }}">Produk</th>
                <th width="{{ $canViewPricing ? '10%' : '12%' }}">Merek</th>
                <th class="text-center" width="{{ $canViewPricing ? '6%' : '8%' }}">Normal</th>
                <th class="text-center" width="{{ $canViewPricing ? '6%' : '8%' }}">Buruk</th>
                <th class="text-center" width="{{ $canViewPricing ? '6%' : '8%' }}">Rusak</th>
                <th class="text-center" width="{{ $canViewPricing ? '8%' : '10%' }}">Total Stok</th>
                <th class="text-center" width="{{ $canViewPricing ? '8%' : '10%' }}">Exp < 1Thn</th>
                <th class="text-center" width="{{ $canViewPricing ? '8%' : '10%' }}">Exp < 6Bln</th>
                <th class="text-center" width="{{ $canViewPricing ? '6%' : '8%' }}">Expired</th>
                @if($canViewPricing)
                    <th class="text-right" width="12%">Nilai Stok</th>
                @endif
            </tr>
        </thead>
        <tbody>
            @forelse ($singleProducts as $index => $product)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $product->name }}</td>
                    <td>{{ $product->brand->name ?? '-' }}</td>
                    <td class="text-center">{{ $product->stock_batches_sum_quantity ?? 0 }}</td>
                    <td class="text-center">{{ $product->stock_batches_sum_bad_quantity ?? 0 }}</td>
                    <td class="text-center">{{ $product->stock_batches_sum_unusable_quantity ?? 0 }}</td>
                    <td class="text-center">{{ ($product->stock_batches_sum_quantity ?? 0) + ($product->stock_batches_sum_bad_quantity ?? 0) }}</td>
                    <td class="text-center">
                        @if($product->stock_expiring_1_year > 0)
                            <span class="badge badge-warning">{{ $product->stock_expiring_1_year }}</span>
                        @else
                            <span class="badge badge-gray">0</span>
                        @endif
                    </td>
                    <td class="text-center">
                        @if($product->stock_expiring_6_months > 0)
                            <span class="badge badge-danger">{{ $product->stock_expiring_6_months }}</span>
                        @else
                            <span class="badge badge-gray">0</span>
                        @endif
                    </td>
                    <td class="text-center">
                        @if($product->stock_expired > 0)
                            <span class="badge badge-danger">{{ $product->stock_expired }}</span>
                        @else
                            <span class="badge badge-gray">0</span>
                        @endif
                    </td>
                    @if($canViewPricing)
                        <td class="text-right">Rp {{ number_format($product->stock_value, 0, ',', '.') }}</td>
                    @endif
                </tr>
            @empty
                <tr>
                    <td colspan="{{ $canViewPricing ? '11' : '10' }}" class="text-center">Tidak ada produk satuan tersedia</td>
                </tr>
            @endforelse

            @if($singleProducts->count() > 0)
                @php
                    $totalStockValue = $singleProducts->sum('stock_value');
                    $totalNormalStock = $singleProducts->sum('stock_batches_sum_quantity');
                    $totalBadStock = $singleProducts->sum('stock_batches_sum_bad_quantity');
                    $totalUnusableStock = $singleProducts->sum('stock_batches_sum_unusable_quantity');
                    $totalStock = $totalNormalStock + $totalBadStock;
                    $totalExpiring1Year = $singleProducts->sum('stock_expiring_1_year');
                    $totalExpiring6Months = $singleProducts->sum('stock_expiring_6_months');
                    $totalExpired = $singleProducts->sum('stock_expired');
                @endphp
                <tr class="total-row">
                    <td class="text-center"><strong>TOTAL</strong></td>
                    <td colspan="2"><strong>PRODUK SATUAN</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalNormalStock) }}</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalBadStock) }}</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalUnusableStock) }}</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalStock) }}</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalExpiring1Year) }}</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalExpiring6Months) }}</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalExpired) }}</strong></td>
                    @if($canViewPricing)
                        <td class="text-right"><strong>Rp {{ number_format($totalStockValue, 0, ',', '.') }}</strong></td>
                    @endif
                </tr>
            @endif
        </tbody>
    </table>

    <!-- Tabel Produk Paket -->
    @if($packageProducts->count() > 0)
        <h2 style="font-size: 14px; margin-bottom: 10px; margin-top: 30px;">PRODUK PAKET</h2>
        <table>
            <thead>
                <tr>
                    <th class="text-center" width="5%">No</th>
                    <th width="{{ $canViewPricing ? '45%' : '50%' }}">Produk Paket</th>
                    <th width="{{ $canViewPricing ? '25%' : '25%' }}">Merek</th>
                    <th class="text-center" width="{{ $canViewPricing ? '25%' : '20%' }}">Estimasi Stok</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($packageProducts as $index => $package)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ $package->name }}</td>
                        <td>{{ $package->brand->name ?? '-' }}</td>
                        <td class="text-center">
                            <span class="badge {{ $package->estimated_package_stock > 0 ? 'badge-warning' : 'badge-gray' }}">
                                {{ $package->estimated_package_stock }} paket
                            </span>
                        </td>
                    </tr>
                @endforeach

                @php
                    $totalPackageStock = $packageProducts->sum('estimated_package_stock');
                @endphp
                <tr class="total-row">
                    <td class="text-center"><strong>TOTAL</strong></td>
                    <td colspan="2"><strong>PRODUK PAKET</strong></td>
                    <td class="text-center"><strong>{{ number_format($totalPackageStock) }} paket</strong></td>
                </tr>
            </tbody>
        </table>
    @endif

    <div class="footer">
        © {{ date('Y') }} Sistem Inventory Gudang
    </div>
</body>
</html>