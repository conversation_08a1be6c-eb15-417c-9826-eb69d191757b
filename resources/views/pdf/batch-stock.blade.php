<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title><PERSON><PERSON><PERSON> per Batch</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            padding: 0;
            font-size: 18px;
        }
        .meta {
            margin-bottom: 15px;
            text-align: right;
            font-size: 11px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            font-size: 10px;
            margin-top: 30px;
            color: #666;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .product-header {
            background-color: #e7f3fe;
            padding: 8px;
            margin-top: 15px;
            margin-bottom: 5px;
            border: 1px solid #c3e6fe;
            border-radius: 3px;
        }
        .batch-table {
            margin-left: 15px;
            margin-bottom: 15px;
            width: calc(100% - 15px);
        }
        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>LAPORAN STOK PER BATCH</h1>
    </div>

    <div class="meta">
        Tanggal Laporan: {{ $date }}
    </div>

    @forelse ($products as $product)
        <div class="product-header">
            <strong>{{ $product->name }}</strong> ({{ $product->brand->name ?? 'Tanpa Merek' }})
            - Normal: {{ $product->stockBatches->sum('quantity') }},
            Buruk: {{ $product->stockBatches->sum('bad_quantity') }},
            Rusak: {{ $product->stockBatches->sum('unusable_quantity') }},
            Total Stok: {{ $product->stockBatches->sum('quantity') + $product->stockBatches->sum('bad_quantity') }}
        </div>

        <table class="batch-table">
            <thead>
                <tr>
                    <th class="text-center" width="5%">No</th>
                    <th width="{{ $canViewPricing ? '20%' : '25%' }}">Tanggal Kedaluwarsa</th>
                    @if($canViewPricing)
                        <th class="text-right" width="20%">Harga Beli</th>
                    @endif
                    <th class="text-center" width="{{ $canViewPricing ? '12%' : '15%' }}">Normal</th>
                    <th class="text-center" width="{{ $canViewPricing ? '12%' : '15%' }}">Buruk</th>
                    <th class="text-center" width="{{ $canViewPricing ? '12%' : '15%' }}">Rusak</th>
                    <th class="text-center" width="{{ $canViewPricing ? '19%' : '25%' }}">Total Stok</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($product->stockBatches as $index => $batch)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ $batch->expiration_date ? $batch->expiration_date->format('d/m/Y') : 'Tidak Ada' }}</td>
                        @if($canViewPricing)
                            <td class="text-right">Rp {{ number_format($batch->purchase_price, 0, ',', '.') }}</td>
                        @endif
                        <td class="text-center">{{ $batch->quantity }}</td>
                        <td class="text-center">{{ $batch->bad_quantity }}</td>
                        <td class="text-center">{{ $batch->unusable_quantity }}</td>
                        <td class="text-center">{{ $batch->quantity + $batch->bad_quantity }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="{{ $canViewPricing ? '7' : '6' }}" class="text-center">Tidak ada batch stok tersedia</td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        @if (!$loop->last)
            <div class="page-break"></div>
        @endif
    @empty
        <p class="text-center">Tidak ada data produk tersedia</p>
    @endforelse

    <div class="footer">
        © {{ date('Y') }} Sistem Inventory Gudang
    </div>
</body>
</html>